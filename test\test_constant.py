"""
Tests for constant.py module.
"""

import unittest
from constant import *


class TestConstants(unittest.TestCase):
    """Test suite for constants."""

    def test_market_types_constants(self):
        """Test market type constants are defined."""
        # Test that constants exist and have expected values
        self.assertEqual(EQUITY, 'EQUITY')
        self.assertEqual(INDEX, 'INDEX')
        self.assertEqual(FUTURES, 'FUTURES')
        self.assertEqual(OPTIONS, 'OPTIONS')

    def test_market_types_list(self):
        """Test market types list contains all expected types."""
        expected_types = ['EQUITY', 'INDEX', 'FUTURES', 'OPTIONS']
        self.assertEqual(MARKET_TYPES, expected_types)

    def test_constants_are_strings(self):
        """Test that all market type constants are strings."""
        self.assertIsInstance(EQUITY, str)
        self.assertIsInstance(INDEX, str)
        self.assertIsInstance(FUTURES, str)
        self.assertIsInstance(OPTIONS, str)

    def test_market_types_list_is_list(self):
        """Test that MARKET_TYPES is a list."""
        self.assertIsInstance(MARKET_TYPES, list)
        self.assertEqual(len(MARKET_TYPES), 4)


if __name__ == '__main__':
    unittest.main()
