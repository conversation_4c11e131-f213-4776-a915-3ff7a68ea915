"""
Comprehensive tests for main.py entry point functionality.
Tests cover application startup, error handling, and workflow orchestration.
"""

import unittest
import tempfile
import os
import sys
from unittest.mock import Mock, patch, MagicMock
from io import StringIO

# Import main module
import main


class TestMainFunctionality(unittest.TestCase):
    """Test suite for main.py functionality."""

    def setUp(self):
        """Set up test fixtures."""
        self.original_cwd = os.getcwd()
        self.temp_dir = tempfile.mkdtemp()
        os.chdir(self.temp_dir)

    def tearDown(self):
        """Clean up test fixtures."""
        os.chdir(self.original_cwd)

    def test_print_banner(self):
        """Test banner printing functionality."""
        # Capture stdout
        captured_output = StringIO()
        with patch('sys.stdout', captured_output):
            main.print_banner()
        
        output = captured_output.getvalue()
        
        # Check that banner contains expected content
        self.assertIn("UNIFIED SCANNER", output)
        self.assertIn("EQUITY", output)
        self.assertIn("INDEX", output)
        self.assertIn("FUTURES", output)
        self.assertIn("OPTIONS", output)

    def test_check_prerequisites_missing_config(self):
        """Test prerequisites check with missing config file."""
        # Ensure config.yaml doesn't exist
        if os.path.exists("config.yaml"):
            os.remove("config.yaml")
        
        # Capture stdout
        captured_output = StringIO()
        with patch('sys.stdout', captured_output):
            result = main.check_prerequisites()
        
        output = captured_output.getvalue()
        
        # Should return False and mention missing files
        self.assertFalse(result)
        self.assertIn("config.yaml", output)

    def test_check_prerequisites_with_config(self):
        """Test prerequisites check with config file present."""
        # Create dummy config file
        with open("config.yaml", "w") as f:
            f.write("test: config")
        
        # Mock imports to succeed
        with patch('builtins.__import__') as mock_import:
            mock_import.return_value = Mock()
            
            # Capture stdout
            captured_output = StringIO()
            with patch('sys.stdout', captured_output):
                result = main.check_prerequisites()
        
        output = captured_output.getvalue()
        
        # Should return True
        self.assertTrue(result)
        self.assertIn("prerequisites satisfied", output)

    def test_check_prerequisites_missing_packages(self):
        """Test prerequisites check with missing packages."""
        # Create dummy config file
        with open("config.yaml", "w") as f:
            f.write("test: config")
        
        # Mock import to fail
        with patch('builtins.__import__') as mock_import:
            mock_import.side_effect = ImportError("No module named 'yaml'")
            
            # Capture stdout
            captured_output = StringIO()
            with patch('sys.stdout', captured_output):
                result = main.check_prerequisites()
        
        output = captured_output.getvalue()
        
        # Should return False and mention missing package
        self.assertFalse(result)
        self.assertIn("Missing required package", output)

    def test_check_prerequisites_csv_files_missing(self):
        """Test prerequisites check mentions missing CSV files."""
        # Create dummy config file
        with open("config.yaml", "w") as f:
            f.write("test: config")
        
        # Ensure CSV files don't exist
        for csv_file in ["NSE_CM.csv", "NSE_FO.csv"]:
            if os.path.exists(csv_file):
                os.remove(csv_file)
        
        # Mock imports to succeed
        with patch('builtins.__import__') as mock_import:
            mock_import.return_value = Mock()
            
            # Capture stdout
            captured_output = StringIO()
            with patch('sys.stdout', captured_output):
                result = main.check_prerequisites()
        
        output = captured_output.getvalue()
        
        # Should still return True but mention CSV files will be downloaded
        self.assertTrue(result)
        self.assertIn("CSV files will be downloaded", output)

    @patch('main.setup_logging')
    @patch('main.UnifiedScanner')
    def test_main_function_success(self, mock_scanner_class, mock_setup_logging):
        """Test main function with successful execution."""
        # Mock logger
        mock_logger = Mock()
        mock_setup_logging.return_value = mock_logger
        
        # Mock scanner
        mock_scanner = Mock()
        mock_scanner_class.return_value = mock_scanner
        mock_scanner.run_unified_scan.return_value = True
        
        # Run main function
        result = main.main()
        
        # Should return True for success
        self.assertTrue(result)
        
        # Verify logging was set up
        mock_setup_logging.assert_called_once()
        
        # Verify scanner was created and run
        mock_scanner_class.assert_called_once_with("config.yaml")
        mock_scanner.run_unified_scan.assert_called_once()

    @patch('main.setup_logging')
    @patch('main.UnifiedScanner')
    def test_main_function_scanner_failure(self, mock_scanner_class, mock_setup_logging):
        """Test main function with scanner failure."""
        # Mock logger
        mock_logger = Mock()
        mock_setup_logging.return_value = mock_logger
        
        # Mock scanner to fail
        mock_scanner = Mock()
        mock_scanner_class.return_value = mock_scanner
        mock_scanner.run_unified_scan.return_value = False
        
        # Run main function
        result = main.main()
        
        # Should return False for failure
        self.assertFalse(result)

    @patch('main.setup_logging')
    @patch('main.UnifiedScanner')
    def test_main_function_keyboard_interrupt(self, mock_scanner_class, mock_setup_logging):
        """Test main function with keyboard interrupt."""
        # Mock logger
        mock_logger = Mock()
        mock_setup_logging.return_value = mock_logger
        
        # Mock scanner to raise KeyboardInterrupt
        mock_scanner = Mock()
        mock_scanner_class.return_value = mock_scanner
        mock_scanner.run_unified_scan.side_effect = KeyboardInterrupt()
        
        # Capture stdout
        captured_output = StringIO()
        with patch('sys.stdout', captured_output):
            result = main.main()
        
        output = captured_output.getvalue()
        
        # Should return False and log interruption
        self.assertFalse(result)
        self.assertIn("interrupted by user", output)

    @patch('main.setup_logging')
    @patch('main.UnifiedScanner')
    def test_main_function_exception(self, mock_scanner_class, mock_setup_logging):
        """Test main function with general exception."""
        # Mock logger
        mock_logger = Mock()
        mock_setup_logging.return_value = mock_logger
        
        # Mock scanner to raise exception
        mock_scanner = Mock()
        mock_scanner_class.return_value = mock_scanner
        mock_scanner.run_unified_scan.side_effect = Exception("Test error")
        
        # Capture stdout
        captured_output = StringIO()
        with patch('sys.stdout', captured_output):
            result = main.main()
        
        output = captured_output.getvalue()
        
        # Should return False and log error
        self.assertFalse(result)
        self.assertIn("Application failed", output)
        self.assertIn("Test error", output)

    def test_base_nifty50_symbols_constant(self):
        """Test that BASE_NIFTY50_SYMBOLS constant is properly defined."""
        # Check that constant exists
        self.assertTrue(hasattr(main, 'BASE_NIFTY50_SYMBOLS'))
        
        # Check that it's a list
        self.assertIsInstance(main.BASE_NIFTY50_SYMBOLS, list)
        
        # Check that it has expected number of symbols (should be 50)
        self.assertEqual(len(main.BASE_NIFTY50_SYMBOLS), 50)
        
        # Check that all symbols are strings
        for symbol in main.BASE_NIFTY50_SYMBOLS:
            self.assertIsInstance(symbol, str)
            self.assertGreater(len(symbol), 0)
        
        # Check for some known NIFTY 50 symbols
        expected_symbols = ['RELIANCE', 'TCS', 'HDFCBANK', 'INFY', 'ICICIBANK']
        for symbol in expected_symbols:
            self.assertIn(symbol, main.BASE_NIFTY50_SYMBOLS)

    @patch('main.print_banner')
    @patch('main.check_prerequisites')
    @patch('main.main')
    @patch('os.chdir')
    @patch('os.path.dirname')
    @patch('os.path.abspath')
    @patch('sys.exit')
    def test_main_entry_point_success(self, mock_exit, mock_abspath, mock_dirname, 
                                     mock_chdir, mock_main, mock_check_prereq, mock_banner):
        """Test main entry point with successful execution."""
        # Mock all dependencies
        mock_abspath.return_value = "/test/path"
        mock_dirname.return_value = "/test"
        mock_check_prereq.return_value = True
        mock_main.return_value = True
        
        # Import and run the main entry point
        import importlib
        import main as main_module
        
        # Simulate running as main
        with patch.object(main_module, '__name__', '__main__'):
            try:
                # This would normally call sys.exit, so we catch it
                exec(compile(open('main.py').read(), 'main.py', 'exec'))
            except SystemExit:
                pass
        
        # Verify functions were called
        mock_banner.assert_called_once()
        mock_check_prereq.assert_called_once()

    @patch('main.print_banner')
    @patch('main.check_prerequisites')
    @patch('sys.exit')
    def test_main_entry_point_prereq_failure(self, mock_exit, mock_check_prereq, mock_banner):
        """Test main entry point with prerequisites failure."""
        # Mock prerequisites to fail
        mock_check_prereq.return_value = False
        
        # Create a minimal script to test entry point
        test_script = '''
import sys
sys.path.insert(0, '.')
from main import print_banner, check_prerequisites
print_banner()
if not check_prerequisites():
    sys.exit(1)
'''
        
        # Execute the test script
        with patch('sys.argv', ['test_script']):
            try:
                exec(test_script)
            except SystemExit as e:
                # Should exit with code 1
                self.assertEqual(e.code, 1)


if __name__ == '__main__':
    unittest.main()
