"""
Comprehensive tests for report_generator.py functionality.
Tests cover report generation, file I/O, formatting, and error handling.
"""

import unittest
import tempfile
import os
import shutil
import csv
from datetime import datetime
from unittest.mock import Mock, patch, MagicMock

from config_loader import ConfigLoader
from report_generator import ReportGenerator
from market_type_scanner import FilteredSymbol


class TestReportGenerator(unittest.TestCase):
    """Test suite for report generator functionality."""

    def setUp(self):
        """Set up test fixtures."""
        # Create temporary directory for test outputs
        self.temp_dir = tempfile.mkdtemp()
        
        # Create test configuration
        self.test_config = {
            'general': {
                'output_dir': self.temp_dir,
                'fyers_api_url': ['https://public.fyers.in/sym_details/NSE_FO.csv']
            },
            'market_types': ['EQUITY', 'INDEX', 'FUTURES', 'OPTIONS'],
            'symbols': ['NIFTY', 'BANKNIFTY'],
            'volume_filter': {
                'min_volume': 1000,
                'max_volume': ********
            },
            'ltp_filter': {
                'min_ltp_price': 1.0,
                'max_ltp_price': 1000.0
            },
            'report_filter': {
                'top_n_closest': 30
            }
        }

        # Create temporary config file
        self.temp_config_file = tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False)
        import yaml
        yaml.dump(self.test_config, self.temp_config_file, default_flow_style=False)
        self.temp_config_file.close()

        # Load configuration
        self.config = ConfigLoader(self.temp_config_file.name)
        
        # Create report generator
        self.report_generator = ReportGenerator(self.config)

    def tearDown(self):
        """Clean up test fixtures."""
        os.unlink(self.temp_config_file.name)
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def create_sample_filtered_symbols(self) -> list:
        """Create sample filtered symbols for testing."""
        return [
            FilteredSymbol(
                symbol='NSE:RELIANCE-EQ',
                market_type='EQUITY',
                ltp=2500.0,
                volume=1000000,
                change=10.0,
                change_percent=0.4
            ),
            FilteredSymbol(
                symbol='NSE:TCS-EQ',
                market_type='EQUITY',
                ltp=3500.0,
                volume=500000,
                change=-20.0,
                change_percent=-0.57
            ),
            FilteredSymbol(
                symbol='NSE:NIFTY50-INDEX',
                market_type='INDEX',
                ltp=25000.0,
                volume=0,
                change=50.0,
                change_percent=0.2
            ),
            FilteredSymbol(
                symbol='NSE:NIFTY24JULFUT',
                market_type='FUTURES',
                ltp=25050.0,
                volume=500000,
                change=25.0,
                change_percent=0.1
            ),
            FilteredSymbol(
                symbol='NSE:NIFTY24JUL25000CE',
                market_type='OPTIONS',
                ltp=150.0,
                volume=100000,
                change=5.0,
                change_percent=3.45
            )
        ]

    def test_report_generator_initialization(self):
        """Test report generator initialization."""
        self.assertIsNotNone(self.report_generator)
        self.assertEqual(self.report_generator.config, self.config)
        self.assertEqual(self.report_generator.output_dir, self.temp_dir)

    def test_generate_reports_creates_files(self):
        """Test that generate_reports creates output files."""
        filtered_symbols = self.create_sample_filtered_symbols()
        
        # Generate reports
        self.report_generator.generate_reports(filtered_symbols)
        
        # Check if output directory exists
        self.assertTrue(os.path.exists(self.temp_dir))
        
        # Check if files were created
        files = os.listdir(self.temp_dir)
        self.assertGreater(len(files), 0)
        
        # Should have CSV files for each market type
        csv_files = [f for f in files if f.endswith('.csv')]
        self.assertGreater(len(csv_files), 0)

    def test_generate_csv_report(self):
        """Test CSV report generation."""
        filtered_symbols = self.create_sample_filtered_symbols()
        
        # Generate CSV report
        csv_filename = self.report_generator.generate_csv_report(filtered_symbols, 'test_report')
        
        # Check if file was created
        self.assertTrue(os.path.exists(csv_filename))
        
        # Check CSV content
        with open(csv_filename, 'r', newline='') as csvfile:
            reader = csv.DictReader(csvfile)
            rows = list(reader)
            
            # Should have same number of rows as filtered symbols
            self.assertEqual(len(rows), len(filtered_symbols))
            
            # Check headers
            expected_headers = ['Symbol', 'Market_Type', 'LTP', 'Volume', 'Change', 'Change_Percent']
            for header in expected_headers:
                self.assertIn(header, reader.fieldnames)

    def test_generate_text_report(self):
        """Test text report generation."""
        filtered_symbols = self.create_sample_filtered_symbols()
        
        # Generate text report
        txt_filename = self.report_generator.generate_text_report(filtered_symbols, 'test_report')
        
        # Check if file was created
        self.assertTrue(os.path.exists(txt_filename))
        
        # Check text content
        with open(txt_filename, 'r') as txtfile:
            content = txtfile.read()
            
            # Should contain symbol information
            self.assertIn('RELIANCE', content)
            self.assertIn('TCS', content)
            self.assertIn('NIFTY', content)

    def test_generate_summary_report(self):
        """Test summary report generation."""
        filtered_symbols = self.create_sample_filtered_symbols()
        
        # Generate summary report
        summary_filename = self.report_generator.generate_summary_report(filtered_symbols)
        
        # Check if file was created
        self.assertTrue(os.path.exists(summary_filename))
        
        # Check summary content
        with open(summary_filename, 'r') as summaryfile:
            content = summaryfile.read()
            
            # Should contain summary statistics
            self.assertIn('Total Symbols', content)
            self.assertIn('EQUITY', content)
            self.assertIn('INDEX', content)
            self.assertIn('FUTURES', content)
            self.assertIn('OPTIONS', content)

    def test_filter_symbols_by_market_type(self):
        """Test filtering symbols by market type."""
        filtered_symbols = self.create_sample_filtered_symbols()
        
        # Filter equity symbols
        equity_symbols = self.report_generator.filter_symbols_by_market_type(filtered_symbols, 'EQUITY')
        self.assertEqual(len(equity_symbols), 2)  # RELIANCE and TCS
        
        # Filter index symbols
        index_symbols = self.report_generator.filter_symbols_by_market_type(filtered_symbols, 'INDEX')
        self.assertEqual(len(index_symbols), 1)  # NIFTY50-INDEX
        
        # Filter futures symbols
        futures_symbols = self.report_generator.filter_symbols_by_market_type(filtered_symbols, 'FUTURES')
        self.assertEqual(len(futures_symbols), 1)  # NIFTY24JULFUT
        
        # Filter options symbols
        options_symbols = self.report_generator.filter_symbols_by_market_type(filtered_symbols, 'OPTIONS')
        self.assertEqual(len(options_symbols), 1)  # NIFTY24JUL25000CE

    def test_sort_symbols_by_criteria(self):
        """Test sorting symbols by different criteria."""
        filtered_symbols = self.create_sample_filtered_symbols()
        
        # Sort by LTP (descending)
        sorted_by_ltp = self.report_generator.sort_symbols_by_criteria(filtered_symbols, 'ltp', reverse=True)
        self.assertEqual(sorted_by_ltp[0].symbol, 'NSE:NIFTY24JULFUT')  # Highest LTP
        
        # Sort by volume (descending)
        sorted_by_volume = self.report_generator.sort_symbols_by_criteria(filtered_symbols, 'volume', reverse=True)
        self.assertEqual(sorted_by_volume[0].symbol, 'NSE:RELIANCE-EQ')  # Highest volume
        
        # Sort by change percent (ascending)
        sorted_by_change = self.report_generator.sort_symbols_by_criteria(filtered_symbols, 'change_percent', reverse=False)
        self.assertEqual(sorted_by_change[0].symbol, 'NSE:TCS-EQ')  # Most negative change

    def test_apply_top_n_filter(self):
        """Test applying top N filter."""
        filtered_symbols = self.create_sample_filtered_symbols()
        
        # Apply top 3 filter
        top_3_symbols = self.report_generator.apply_top_n_filter(filtered_symbols, 3)
        self.assertEqual(len(top_3_symbols), 3)
        
        # Apply top 10 filter (more than available)
        top_10_symbols = self.report_generator.apply_top_n_filter(filtered_symbols, 10)
        self.assertEqual(len(top_10_symbols), len(filtered_symbols))  # Should return all available

    def test_format_symbol_for_output(self):
        """Test symbol formatting for output."""
        symbol = FilteredSymbol(
            symbol='NSE:RELIANCE-EQ',
            market_type='EQUITY',
            ltp=2500.75,
            volume=1000000,
            change=10.25,
            change_percent=0.41
        )
        
        formatted = self.report_generator.format_symbol_for_output(symbol)
        
        # Should return formatted dictionary
        self.assertIsInstance(formatted, dict)
        self.assertIn('Symbol', formatted)
        self.assertIn('LTP', formatted)
        self.assertIn('Volume', formatted)
        
        # Check number formatting
        self.assertEqual(formatted['LTP'], '2500.75')
        self.assertEqual(formatted['Volume'], '1,000,000')

    def test_generate_market_type_specific_reports(self):
        """Test generation of market type specific reports."""
        filtered_symbols = self.create_sample_filtered_symbols()
        
        # Generate market type specific reports
        self.report_generator.generate_market_type_specific_reports(filtered_symbols)
        
        # Check if market type specific files were created
        files = os.listdir(self.temp_dir)
        
        # Should have files for each market type
        market_type_files = [f for f in files if any(mt.lower() in f.lower() for mt in ['equity', 'index', 'futures', 'options'])]
        self.assertGreater(len(market_type_files), 0)

    def test_error_handling_empty_symbols(self):
        """Test error handling with empty symbol list."""
        empty_symbols = []
        
        # Should handle empty list gracefully
        try:
            self.report_generator.generate_reports(empty_symbols)
            
            # Should still create output directory
            self.assertTrue(os.path.exists(self.temp_dir))
        except Exception as e:
            self.fail(f"Failed to handle empty symbols list: {e}")

    def test_error_handling_invalid_output_directory(self):
        """Test error handling with invalid output directory."""
        # Create config with invalid output directory
        invalid_config = self.test_config.copy()
        invalid_config['general']['output_dir'] = '/invalid/path/that/does/not/exist'
        
        temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False)
        import yaml
        yaml.dump(invalid_config, temp_file, default_flow_style=False)
        temp_file.close()
        
        try:
            config_invalid = ConfigLoader(temp_file.name)
            report_gen_invalid = ReportGenerator(config_invalid)
            
            # Should handle invalid directory gracefully
            filtered_symbols = self.create_sample_filtered_symbols()
            report_gen_invalid.generate_reports(filtered_symbols)
            
        except Exception:
            # Exception is acceptable for invalid directory
            pass
        finally:
            os.unlink(temp_file.name)

    def test_timestamp_in_filenames(self):
        """Test that generated files include timestamps."""
        filtered_symbols = self.create_sample_filtered_symbols()
        
        # Generate reports
        self.report_generator.generate_reports(filtered_symbols)
        
        # Check if files have timestamps
        files = os.listdir(self.temp_dir)
        
        # At least one file should contain date/time information
        timestamp_files = [f for f in files if any(char.isdigit() for char in f)]
        self.assertGreater(len(timestamp_files), 0)

    def test_file_permissions(self):
        """Test that generated files have correct permissions."""
        filtered_symbols = self.create_sample_filtered_symbols()
        
        # Generate reports
        self.report_generator.generate_reports(filtered_symbols)
        
        # Check file permissions
        files = os.listdir(self.temp_dir)
        for filename in files:
            filepath = os.path.join(self.temp_dir, filename)
            if os.path.isfile(filepath):
                # Should be readable
                self.assertTrue(os.access(filepath, os.R_OK))

    def test_large_dataset_handling(self):
        """Test handling of large datasets."""
        # Create large dataset
        large_symbols = []
        for i in range(1000):
            large_symbols.append(FilteredSymbol(
                symbol=f'NSE:SYMBOL{i}-EQ',
                market_type='EQUITY',
                ltp=100.0 + i,
                volume=1000 + i,
                change=i % 10,
                change_percent=(i % 10) / 100.0
            ))
        
        # Should handle large dataset without issues
        try:
            self.report_generator.generate_reports(large_symbols)
            
            # Check if files were created
            files = os.listdir(self.temp_dir)
            self.assertGreater(len(files), 0)
            
        except Exception as e:
            self.fail(f"Failed to handle large dataset: {e}")


if __name__ == '__main__':
    unittest.main()
