"""
Tests for option_utils.py module.
"""

import unittest
from datetime import datetime
from option_utils import *


class TestOptionUtils(unittest.TestCase):
    """Test suite for option utilities."""

    def test_black_scholes_delta_call(self):
        """Test Black-Scholes delta calculation for call options."""
        # Test call option delta
        delta = black_scholes_delta(
            S=100.0,  # Spot price
            K=100.0,  # Strike price
            T=0.25,   # Time to expiry (3 months)
            r=0.05,   # Risk-free rate
            sigma=0.2,  # Volatility
            option_type='CE'
        )
        
        # Delta should be between 0 and 1 for call options
        self.assertGreater(delta, 0)
        self.assertLess(delta, 1)
        
        # ATM call option should have delta around 0.5
        self.assertAlmostEqual(delta, 0.5, delta=0.1)

    def test_black_scholes_delta_put(self):
        """Test Black-Scholes delta calculation for put options."""
        # Test put option delta
        delta = black_scholes_delta(
            S=100.0,  # Spot price
            K=100.0,  # Strike price
            T=0.25,   # Time to expiry (3 months)
            r=0.05,   # Risk-free rate
            sigma=0.2,  # Volatility
            option_type='PE'
        )
        
        # Delta should be between -1 and 0 for put options
        self.assertLess(delta, 0)
        self.assertGreater(delta, -1)
        
        # ATM put option should have delta around -0.5
        self.assertAlmostEqual(delta, -0.5, delta=0.1)

    def test_black_scholes_delta_itm_call(self):
        """Test Black-Scholes delta for in-the-money call option."""
        # ITM call option (spot > strike)
        delta = black_scholes_delta(
            S=110.0,  # Spot price
            K=100.0,  # Strike price
            T=0.25,   # Time to expiry
            r=0.05,   # Risk-free rate
            sigma=0.2,  # Volatility
            option_type='CE'
        )
        
        # ITM call should have higher delta (closer to 1)
        self.assertGreater(delta, 0.5)
        self.assertLess(delta, 1)

    def test_black_scholes_delta_otm_call(self):
        """Test Black-Scholes delta for out-of-the-money call option."""
        # OTM call option (spot < strike)
        delta = black_scholes_delta(
            S=90.0,   # Spot price
            K=100.0,  # Strike price
            T=0.25,   # Time to expiry
            r=0.05,   # Risk-free rate
            sigma=0.2,  # Volatility
            option_type='CE'
        )
        
        # OTM call should have lower delta (closer to 0)
        self.assertGreater(delta, 0)
        self.assertLess(delta, 0.5)

    def test_black_scholes_delta_edge_cases(self):
        """Test Black-Scholes delta with edge cases."""
        # Test with very short time to expiry
        delta_short = black_scholes_delta(
            S=100.0, K=100.0, T=0.001, r=0.05, sigma=0.2, option_type='CE'
        )
        self.assertIsInstance(delta_short, float)
        
        # Test with very long time to expiry
        delta_long = black_scholes_delta(
            S=100.0, K=100.0, T=2.0, r=0.05, sigma=0.2, option_type='CE'
        )
        self.assertIsInstance(delta_long, float)
        
        # Test with high volatility
        delta_high_vol = black_scholes_delta(
            S=100.0, K=100.0, T=0.25, r=0.05, sigma=0.8, option_type='CE'
        )
        self.assertIsInstance(delta_high_vol, float)

    def test_is_weekly_expiry_valid(self):
        """Test weekly expiry validation."""
        # Test valid weekly expiry
        valid_result = is_weekly_expiry_valid(2024, 'JUL_04')
        self.assertIsInstance(valid_result, bool)
        
        # Test invalid weekly expiry format
        invalid_result = is_weekly_expiry_valid(2024, 'INVALID')
        self.assertFalse(invalid_result)

    def test_parse_option_symbol(self):
        """Test option symbol parsing."""
        # Test valid option symbol
        symbol = 'NSE:NIFTY24JUL25000CE'
        parsed = parse_option_symbol(symbol)
        
        if parsed:
            self.assertIn('underlying', parsed)
            self.assertIn('expiry', parsed)
            self.assertIn('strike', parsed)
            self.assertIn('option_type', parsed)

    def test_calculate_option_greeks(self):
        """Test option Greeks calculation."""
        greeks = calculate_option_greeks(
            S=100.0,  # Spot price
            K=100.0,  # Strike price
            T=0.25,   # Time to expiry
            r=0.05,   # Risk-free rate
            sigma=0.2,  # Volatility
            option_type='CE'
        )
        
        # Should return a dictionary with Greeks
        self.assertIsInstance(greeks, dict)
        self.assertIn('delta', greeks)
        self.assertIn('gamma', greeks)
        self.assertIn('theta', greeks)
        self.assertIn('vega', greeks)

    def test_get_option_moneyness(self):
        """Test option moneyness calculation."""
        # ATM option
        moneyness_atm = get_option_moneyness(100.0, 100.0)
        self.assertEqual(moneyness_atm, 'ATM')
        
        # ITM call option
        moneyness_itm = get_option_moneyness(110.0, 100.0, 'CE')
        self.assertEqual(moneyness_itm, 'ITM')
        
        # OTM call option
        moneyness_otm = get_option_moneyness(90.0, 100.0, 'CE')
        self.assertEqual(moneyness_otm, 'OTM')

    def test_calculate_implied_volatility(self):
        """Test implied volatility calculation."""
        try:
            iv = calculate_implied_volatility(
                market_price=5.0,
                S=100.0,
                K=100.0,
                T=0.25,
                r=0.05,
                option_type='CE'
            )
            
            # Should return a positive volatility
            if iv is not None:
                self.assertGreater(iv, 0)
                self.assertLess(iv, 5.0)  # Reasonable upper bound
        except Exception:
            # IV calculation might fail with certain inputs
            pass

    def test_get_time_to_expiry(self):
        """Test time to expiry calculation."""
        # Test with future date
        future_date = datetime(2024, 12, 31)
        current_date = datetime(2024, 6, 30)
        
        time_to_expiry = get_time_to_expiry(future_date, current_date)
        
        # Should return positive time for future expiry
        self.assertGreater(time_to_expiry, 0)

    def test_format_option_symbol(self):
        """Test option symbol formatting."""
        formatted = format_option_symbol(
            underlying='NIFTY',
            expiry_year=2024,
            expiry_month='JUL',
            strike=25000,
            option_type='CE'
        )
        
        expected = 'NSE:NIFTY24JUL25000CE'
        self.assertEqual(formatted, expected)


if __name__ == '__main__':
    unittest.main()
