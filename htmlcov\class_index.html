<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_718ce007.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">12%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button current">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.0">coverage.py v7.8.0</a>,
            created at 2025-07-23 17:35 +0530
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">class<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="config_loader_py.html#t13">config_loader.py</a></td>
                <td class="name left"><a href="config_loader_py.html#t13"><data value='ConfigLoader'>ConfigLoader</data></a></td>
                <td>117</td>
                <td>81</td>
                <td>0</td>
                <td class="right" data-ratio="36 117">31%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_loader_py.html">config_loader.py</a></td>
                <td class="name left"><a href="config_loader_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>93</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="84 93">90%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="constant_py.html">constant.py</a></td>
                <td class="name left"><a href="constant_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="debug_nifty_weekly_py.html">debug_nifty_weekly.py</a></td>
                <td class="name left"><a href="debug_nifty_weekly_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>143</td>
                <td>143</td>
                <td>0</td>
                <td class="right" data-ratio="0 143">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="fyers_client_py.html#t30">fyers_client.py</a></td>
                <td class="name left"><a href="fyers_client_py.html#t30"><data value='MarketData'>MarketData</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="fyers_client_py.html#t44">fyers_client.py</a></td>
                <td class="name left"><a href="fyers_client_py.html#t44"><data value='OHLCData'>OHLCData</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="fyers_client_py.html#t53">fyers_client.py</a></td>
                <td class="name left"><a href="fyers_client_py.html#t53"><data value='FyersClient'>FyersClient</data></a></td>
                <td>260</td>
                <td>260</td>
                <td>0</td>
                <td class="right" data-ratio="0 260">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="fyers_client_py.html">fyers_client.py</a></td>
                <td class="name left"><a href="fyers_client_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>41</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="41 41">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="fyers_config_py.html#t119">fyers_config.py</a></td>
                <td class="name left"><a href="fyers_config_py.html#t119"><data value='FyersConfig'>FyersConfig</data></a></td>
                <td>90</td>
                <td>90</td>
                <td>0</td>
                <td class="right" data-ratio="0 90">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="fyers_config_py.html">fyers_config.py</a></td>
                <td class="name left"><a href="fyers_config_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>74</td>
                <td>49</td>
                <td>0</td>
                <td class="right" data-ratio="25 74">34%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="fyers_connect_py.html#t22">fyers_connect.py</a></td>
                <td class="name left"><a href="fyers_connect_py.html#t22"><data value='FyersConnect'>FyersConnect</data></a></td>
                <td>529</td>
                <td>529</td>
                <td>0</td>
                <td class="right" data-ratio="0 529">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="fyers_connect_py.html">fyers_connect.py</a></td>
                <td class="name left"><a href="fyers_connect_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>37</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="37 37">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="main_py.html">main.py</a></td>
                <td class="name left"><a href="main_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>64</td>
                <td>64</td>
                <td>0</td>
                <td class="right" data-ratio="0 64">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="market_type_scanner_py.html#t22">market_type_scanner.py</a></td>
                <td class="name left"><a href="market_type_scanner_py.html#t22"><data value='MarketData'>MarketData</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="market_type_scanner_py.html#t36">market_type_scanner.py</a></td>
                <td class="name left"><a href="market_type_scanner_py.html#t36"><data value='FilteredSymbol'>FilteredSymbol</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="market_type_scanner_py.html#t52">market_type_scanner.py</a></td>
                <td class="name left"><a href="market_type_scanner_py.html#t52"><data value='BaseMarketScanner'>BaseMarketScanner</data></a></td>
                <td>279</td>
                <td>243</td>
                <td>0</td>
                <td class="right" data-ratio="36 279">13%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="market_type_scanner_py.html#t605">market_type_scanner.py</a></td>
                <td class="name left"><a href="market_type_scanner_py.html#t605"><data value='EquityScanner'>EquityScanner</data></a></td>
                <td>44</td>
                <td>43</td>
                <td>0</td>
                <td class="right" data-ratio="1 44">2%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="market_type_scanner_py.html#t697">market_type_scanner.py</a></td>
                <td class="name left"><a href="market_type_scanner_py.html#t697"><data value='IndexScanner'>IndexScanner</data></a></td>
                <td>44</td>
                <td>43</td>
                <td>0</td>
                <td class="right" data-ratio="1 44">2%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="market_type_scanner_py.html#t789">market_type_scanner.py</a></td>
                <td class="name left"><a href="market_type_scanner_py.html#t789"><data value='FuturesScanner'>FuturesScanner</data></a></td>
                <td>44</td>
                <td>43</td>
                <td>0</td>
                <td class="right" data-ratio="1 44">2%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="market_type_scanner_py.html#t881">market_type_scanner.py</a></td>
                <td class="name left"><a href="market_type_scanner_py.html#t881"><data value='OptionsScanner'>OptionsScanner</data></a></td>
                <td>290</td>
                <td>290</td>
                <td>0</td>
                <td class="right" data-ratio="0 290">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="market_type_scanner_py.html#t1383">market_type_scanner.py</a></td>
                <td class="name left"><a href="market_type_scanner_py.html#t1383"><data value='MarketTypeScannerFactory'>MarketTypeScannerFactory</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="market_type_scanner_py.html">market_type_scanner.py</a></td>
                <td class="name left"><a href="market_type_scanner_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>86</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="86 86">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="option_utils_py.html">option_utils.py</a></td>
                <td class="name left"><a href="option_utils_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>86</td>
                <td>86</td>
                <td>0</td>
                <td class="right" data-ratio="0 86">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="options_chain_filter_py.html#t20">options_chain_filter.py</a></td>
                <td class="name left"><a href="options_chain_filter_py.html#t20"><data value='OptionsChainData'>OptionsChainData</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="options_chain_filter_py.html#t38">options_chain_filter.py</a></td>
                <td class="name left"><a href="options_chain_filter_py.html#t38"><data value='OptionsChainFilter'>OptionsChainFilter</data></a></td>
                <td>392</td>
                <td>279</td>
                <td>0</td>
                <td class="right" data-ratio="113 392">29%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="options_chain_filter_py.html">options_chain_filter.py</a></td>
                <td class="name left"><a href="options_chain_filter_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>45</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="45 45">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="pivot_point_core_py.html">pivot_point_core.py</a></td>
                <td class="name left"><a href="pivot_point_core_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>260</td>
                <td>260</td>
                <td>0</td>
                <td class="right" data-ratio="0 260">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="pivot_point_integration_py.html#t22">pivot_point_integration.py</a></td>
                <td class="name left"><a href="pivot_point_integration_py.html#t22"><data value='PivotPointData'>PivotPointData</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="pivot_point_integration_py.html#t34">pivot_point_integration.py</a></td>
                <td class="name left"><a href="pivot_point_integration_py.html#t34"><data value='PivotPointIntegration'>PivotPointIntegration</data></a></td>
                <td>338</td>
                <td>338</td>
                <td>0</td>
                <td class="right" data-ratio="0 338">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="pivot_point_integration_py.html">pivot_point_integration.py</a></td>
                <td class="name left"><a href="pivot_point_integration_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>36</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="35 36">97%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="pivot_points_py.html">pivot_points.py</a></td>
                <td class="name left"><a href="pivot_points_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>86</td>
                <td>77</td>
                <td>0</td>
                <td class="right" data-ratio="9 86">10%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="report_generator_py.html#t19">report_generator.py</a></td>
                <td class="name left"><a href="report_generator_py.html#t19"><data value='ReportGenerator'>ReportGenerator</data></a></td>
                <td>326</td>
                <td>326</td>
                <td>0</td>
                <td class="right" data-ratio="0 326">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="report_generator_py.html">report_generator.py</a></td>
                <td class="name left"><a href="report_generator_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>25</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="0 25">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="symbol_config_py.html">symbol_config.py</a></td>
                <td class="name left"><a href="symbol_config_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>29</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="0 29">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="symbol_downloader_py.html">symbol_downloader.py</a></td>
                <td class="name left"><a href="symbol_downloader_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>131</td>
                <td>131</td>
                <td>0</td>
                <td class="right" data-ratio="0 131">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="technical_indicators_py.html#t8">technical_indicators.py</a></td>
                <td class="name left"><a href="technical_indicators_py.html#t8"><data value='MAEAnalyzer'>MAEAnalyzer</data></a></td>
                <td>50</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="46 50">92%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="technical_indicators_py.html">technical_indicators.py</a></td>
                <td class="name left"><a href="technical_indicators_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8___init___py.html">test\__init__.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_all_market_combinations_py.html#t21">test\test_all_market_combinations.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_all_market_combinations_py.html#t21"><data value='MarketTypeTester'>MarketTypeTester</data></a></td>
                <td>92</td>
                <td>92</td>
                <td>0</td>
                <td class="right" data-ratio="0 92">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_all_market_combinations_py.html">test\test_all_market_combinations.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_all_market_combinations_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>35</td>
                <td>35</td>
                <td>0</td>
                <td class="right" data-ratio="0 35">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_api_integration_py.html#t26">test\test_api_integration.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_api_integration_py.html#t26"><data value='TestAPIIntegration'>TestAPIIntegration</data></a></td>
                <td>109</td>
                <td>109</td>
                <td>0</td>
                <td class="right" data-ratio="0 109">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_api_integration_py.html#t380">test\test_api_integration.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_api_integration_py.html#t380"><data value='TestAPIErrorHandling'>TestAPIErrorHandling</data></a></td>
                <td>94</td>
                <td>94</td>
                <td>0</td>
                <td class="right" data-ratio="0 94">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_api_integration_py.html#t642">test\test_api_integration.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_api_integration_py.html#t642"><data value='TestAPIIntegrationWithScanners'>TestAPIIntegrationWithScanners</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_api_integration_py.html">test\test_api_integration.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_api_integration_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>69</td>
                <td>69</td>
                <td>0</td>
                <td class="right" data-ratio="0 69">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_comprehensive_py.html#t30">test\test_comprehensive.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_comprehensive_py.html#t30"><data value='ComprehensiveTestSuite'>ComprehensiveTestSuite</data></a></td>
                <td>119</td>
                <td>119</td>
                <td>0</td>
                <td class="right" data-ratio="0 119">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_comprehensive_py.html">test\test_comprehensive.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_comprehensive_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>33</td>
                <td>33</td>
                <td>0</td>
                <td class="right" data-ratio="0 33">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_constant_py.html#t9">test\test_constant.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_constant_py.html#t9"><data value='TestConstants'>TestConstants</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_constant_py.html">test\test_constant.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_constant_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_end_to_end_integration_py.html#t27">test\test_end_to_end_integration.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_end_to_end_integration_py.html#t27"><data value='TestEndToEndIntegration'>TestEndToEndIntegration</data></a></td>
                <td>117</td>
                <td>117</td>
                <td>0</td>
                <td class="right" data-ratio="0 117">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_end_to_end_integration_py.html">test\test_end_to_end_integration.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_end_to_end_integration_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>38</td>
                <td>38</td>
                <td>0</td>
                <td class="right" data-ratio="0 38">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_filter_combinations_mock_py.html#t34">test\test_filter_combinations_mock.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_filter_combinations_mock_py.html#t34"><data value='MockFilterCombinationTestSuite'>MockFilterCombinationTestSuite</data></a></td>
                <td>243</td>
                <td>243</td>
                <td>0</td>
                <td class="right" data-ratio="0 243">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_filter_combinations_mock_py.html">test\test_filter_combinations_mock.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_filter_combinations_mock_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>45</td>
                <td>45</td>
                <td>0</td>
                <td class="right" data-ratio="0 45">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_fixes_validation_py.html">test\test_fixes_validation.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_fixes_validation_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>92</td>
                <td>92</td>
                <td>0</td>
                <td class="right" data-ratio="0 92">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_mae_indicator_py.html#t26">test\test_mae_indicator.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_mae_indicator_py.html#t26"><data value='TestMAEIndicator'>TestMAEIndicator</data></a></td>
                <td>167</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="165 167">99%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_mae_indicator_py.html#t485">test\test_mae_indicator.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_mae_indicator_py.html#t485"><data value='TestMAEIntegrationWithMarketScanners'>TestMAEIntegrationWithMarketScanners</data></a></td>
                <td>30</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="30 30">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_mae_indicator_py.html">test\test_mae_indicator.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_mae_indicator_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>44</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="43 44">98%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_main_py.html#t17">test\test_main.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_main_py.html#t17"><data value='TestMainFunctionality'>TestMainFunctionality</data></a></td>
                <td>123</td>
                <td>123</td>
                <td>0</td>
                <td class="right" data-ratio="0 123">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_main_py.html">test\test_main.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_main_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>42</td>
                <td>42</td>
                <td>0</td>
                <td class="right" data-ratio="0 42">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_main_functionality_py.html">test\test_main_functionality.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_main_functionality_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>124</td>
                <td>124</td>
                <td>0</td>
                <td class="right" data-ratio="0 124">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_main_integration_py.html">test\test_main_integration.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_main_integration_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>96</td>
                <td>96</td>
                <td>0</td>
                <td class="right" data-ratio="0 96">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_multi_market_scanner_py.html#t29">test\test_multi_market_scanner.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_multi_market_scanner_py.html#t29"><data value='MultiMarketScannerTest'>MultiMarketScannerTest</data></a></td>
                <td>129</td>
                <td>129</td>
                <td>0</td>
                <td class="right" data-ratio="0 129">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_multi_market_scanner_py.html">test\test_multi_market_scanner.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_multi_market_scanner_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>35</td>
                <td>35</td>
                <td>0</td>
                <td class="right" data-ratio="0 35">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_nifty_options_comprehensive_py.html#t22">test\test_nifty_options_comprehensive.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_nifty_options_comprehensive_py.html#t22"><data value='TestNiftyOptionsComprehensive'>TestNiftyOptionsComprehensive</data></a></td>
                <td>154</td>
                <td>154</td>
                <td>0</td>
                <td class="right" data-ratio="0 154">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_nifty_options_comprehensive_py.html">test\test_nifty_options_comprehensive.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_nifty_options_comprehensive_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_option_utils_py.html#t10">test\test_option_utils.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_option_utils_py.html#t10"><data value='TestOptionUtils'>TestOptionUtils</data></a></td>
                <td>57</td>
                <td>57</td>
                <td>0</td>
                <td class="right" data-ratio="0 57">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_option_utils_py.html">test\test_option_utils.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_option_utils_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_options_delta_values_py.html#t20">test\test_options_delta_values.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_options_delta_values_py.html#t20"><data value='TestOptionsDeltaValues'>TestOptionsDeltaValues</data></a></td>
                <td>69</td>
                <td>69</td>
                <td>0</td>
                <td class="right" data-ratio="0 69">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_options_delta_values_py.html">test\test_options_delta_values.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_options_delta_values_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>26</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="0 26">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_options_prefiltering_py.html">test\test_options_prefiltering.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_options_prefiltering_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>64</td>
                <td>64</td>
                <td>0</td>
                <td class="right" data-ratio="0 64">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_performance_optimizations_py.html#t18">test\test_performance_optimizations.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_performance_optimizations_py.html#t18"><data value='TestPerformanceOptimizations'>TestPerformanceOptimizations</data></a></td>
                <td>100</td>
                <td>100</td>
                <td>0</td>
                <td class="right" data-ratio="0 100">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_performance_optimizations_py.html">test\test_performance_optimizations.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_performance_optimizations_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_pivot_calculation_types_py.html#t53">test\test_pivot_calculation_types.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_pivot_calculation_types_py.html#t53"><data value='MockMarketData'>test_pivot_calculation_types.MockMarketData</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_pivot_calculation_types_py.html">test\test_pivot_calculation_types.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_pivot_calculation_types_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>52</td>
                <td>52</td>
                <td>0</td>
                <td class="right" data-ratio="0 52">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_pivot_point_core_py.html#t15">test\test_pivot_point_core.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_pivot_point_core_py.html#t15"><data value='TestPivotPointCore'>TestPivotPointCore</data></a></td>
                <td>134</td>
                <td>134</td>
                <td>0</td>
                <td class="right" data-ratio="0 134">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_pivot_point_core_py.html">test\test_pivot_point_core.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_pivot_point_core_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>25</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="0 25">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_pivot_point_options_workflow_py.html#t34">test\test_pivot_point_options_workflow.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_pivot_point_options_workflow_py.html#t34"><data value='SymbolTestData'>SymbolTestData</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_pivot_point_options_workflow_py.html#t44">test\test_pivot_point_options_workflow.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_pivot_point_options_workflow_py.html#t44"><data value='PivotPointOptionsWorkflowTest'>PivotPointOptionsWorkflowTest</data></a></td>
                <td>358</td>
                <td>358</td>
                <td>0</td>
                <td class="right" data-ratio="0 358">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_pivot_point_options_workflow_py.html">test\test_pivot_point_options_workflow.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_pivot_point_options_workflow_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>46</td>
                <td>46</td>
                <td>0</td>
                <td class="right" data-ratio="0 46">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_prefiltering_strike_levels_py.html#t25">test\test_prefiltering_strike_levels.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_prefiltering_strike_levels_py.html#t25"><data value='TestPrefilteringStrikeLevels'>TestPrefilteringStrikeLevels</data></a></td>
                <td>106</td>
                <td>30</td>
                <td>0</td>
                <td class="right" data-ratio="76 106">72%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_prefiltering_strike_levels_py.html">test\test_prefiltering_strike_levels.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_prefiltering_strike_levels_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>43</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="25 43">58%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_report_generator_py.html#t19">test\test_report_generator.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_report_generator_py.html#t19"><data value='TestReportGenerator'>TestReportGenerator</data></a></td>
                <td>124</td>
                <td>124</td>
                <td>0</td>
                <td class="right" data-ratio="0 124">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_report_generator_py.html">test\test_report_generator.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_report_generator_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>31</td>
                <td>31</td>
                <td>0</td>
                <td class="right" data-ratio="0 31">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_symbol_config_py.html#t10">test\test_symbol_config.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_symbol_config_py.html#t10"><data value='TestSymbolConfig'>TestSymbolConfig</data></a></td>
                <td>48</td>
                <td>48</td>
                <td>0</td>
                <td class="right" data-ratio="0 48">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_symbol_config_py.html">test\test_symbol_config.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_symbol_config_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_timeframe_py.html">test\test_timeframe.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_timeframe_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>34</td>
                <td>34</td>
                <td>0</td>
                <td class="right" data-ratio="0 34">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="test_index_fixes_py.html#t94">test_index_fixes.py</a></td>
                <td class="name left"><a href="test_index_fixes_py.html#t94"><data value='MockMarketData'>test_index_pivot_point_calculation.MockMarketData</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="test_index_fixes_py.html">test_index_fixes.py</a></td>
                <td class="name left"><a href="test_index_fixes_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>95</td>
                <td>95</td>
                <td>0</td>
                <td class="right" data-ratio="0 95">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="unified_scanner_py.html#t21">unified_scanner.py</a></td>
                <td class="name left"><a href="unified_scanner_py.html#t21"><data value='UnifiedScanner'>UnifiedScanner</data></a></td>
                <td>195</td>
                <td>195</td>
                <td>0</td>
                <td class="right" data-ratio="0 195">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="unified_scanner_py.html">unified_scanner.py</a></td>
                <td class="name left"><a href="unified_scanner_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>27</td>
                <td>27</td>
                <td>0</td>
                <td class="right" data-ratio="0 27">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="universal_symbol_parser_py.html#t18">universal_symbol_parser.py</a></td>
                <td class="name left"><a href="universal_symbol_parser_py.html#t18"><data value='UniversalSymbol'>UniversalSymbol</data></a></td>
                <td>8</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="1 8">12%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="universal_symbol_parser_py.html#t69">universal_symbol_parser.py</a></td>
                <td class="name left"><a href="universal_symbol_parser_py.html#t69"><data value='UniversalSymbolParser'>UniversalSymbolParser</data></a></td>
                <td>238</td>
                <td>225</td>
                <td>0</td>
                <td class="right" data-ratio="13 238">5%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="universal_symbol_parser_py.html">universal_symbol_parser.py</a></td>
                <td class="name left"><a href="universal_symbol_parser_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>43</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="43 43">100%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>8147</td>
                <td>7146</td>
                <td>0</td>
                <td class="right" data-ratio="1001 8147">12%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.0">coverage.py v7.8.0</a>,
            created at 2025-07-23 17:35 +0530
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
