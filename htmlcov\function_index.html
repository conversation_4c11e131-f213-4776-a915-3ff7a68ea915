<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_718ce007.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">12%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button current">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.0">coverage.py v7.8.0</a>,
            created at 2025-07-23 17:35 +0530
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">function<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="config_loader_py.html#t16">config_loader.py</a></td>
                <td class="name left"><a href="config_loader_py.html#t16"><data value='init__'>ConfigLoader.__init__</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_loader_py.html#t27">config_loader.py</a></td>
                <td class="name left"><a href="config_loader_py.html#t27"><data value='load_config'>ConfigLoader.load_config</data></a></td>
                <td>9</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="5 9">56%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_loader_py.html#t42">config_loader.py</a></td>
                <td class="name left"><a href="config_loader_py.html#t42"><data value='get'>ConfigLoader.get</data></a></td>
                <td>10</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="9 10">90%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_loader_py.html#t68">config_loader.py</a></td>
                <td class="name left"><a href="config_loader_py.html#t68"><data value='env_path'>ConfigLoader.env_path</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_loader_py.html#t73">config_loader.py</a></td>
                <td class="name left"><a href="config_loader_py.html#t73"><data value='output_dir'>ConfigLoader.output_dir</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_loader_py.html#t78">config_loader.py</a></td>
                <td class="name left"><a href="config_loader_py.html#t78"><data value='fyers_api_url'>ConfigLoader.fyers_api_url</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_loader_py.html#t88">config_loader.py</a></td>
                <td class="name left"><a href="config_loader_py.html#t88"><data value='market_types'>ConfigLoader.market_types</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_loader_py.html#t94">config_loader.py</a></td>
                <td class="name left"><a href="config_loader_py.html#t94"><data value='symbols'>ConfigLoader.symbols</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_loader_py.html#t98">config_loader.py</a></td>
                <td class="name left"><a href="config_loader_py.html#t98"><data value='get_target_symbols'>ConfigLoader.get_target_symbols</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_loader_py.html#t104">config_loader.py</a></td>
                <td class="name left"><a href="config_loader_py.html#t104"><data value='min_volume'>ConfigLoader.min_volume</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_loader_py.html#t109">config_loader.py</a></td>
                <td class="name left"><a href="config_loader_py.html#t109"><data value='max_volume'>ConfigLoader.max_volume</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_loader_py.html#t114">config_loader.py</a></td>
                <td class="name left"><a href="config_loader_py.html#t114"><data value='min_ltp_price'>ConfigLoader.min_ltp_price</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_loader_py.html#t119">config_loader.py</a></td>
                <td class="name left"><a href="config_loader_py.html#t119"><data value='max_ltp_price'>ConfigLoader.max_ltp_price</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_loader_py.html#t125">config_loader.py</a></td>
                <td class="name left"><a href="config_loader_py.html#t125"><data value='options_strike_level'>ConfigLoader.options_strike_level</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_loader_py.html#t130">config_loader.py</a></td>
                <td class="name left"><a href="config_loader_py.html#t130"><data value='options_target_months'>ConfigLoader.options_target_months</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_loader_py.html#t135">config_loader.py</a></td>
                <td class="name left"><a href="config_loader_py.html#t135"><data value='options_expiry_type'>ConfigLoader.options_expiry_type</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_loader_py.html#t141">config_loader.py</a></td>
                <td class="name left"><a href="config_loader_py.html#t141"><data value='timeframe_interval'>ConfigLoader.timeframe_interval</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_loader_py.html#t146">config_loader.py</a></td>
                <td class="name left"><a href="config_loader_py.html#t146"><data value='days_to_fetch'>ConfigLoader.days_to_fetch</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_loader_py.html#t152">config_loader.py</a></td>
                <td class="name left"><a href="config_loader_py.html#t152"><data value='mae_enabled'>ConfigLoader.mae_enabled</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_loader_py.html#t157">config_loader.py</a></td>
                <td class="name left"><a href="config_loader_py.html#t157"><data value='mae_length'>ConfigLoader.mae_length</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_loader_py.html#t162">config_loader.py</a></td>
                <td class="name left"><a href="config_loader_py.html#t162"><data value='mae_source'>ConfigLoader.mae_source</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_loader_py.html#t167">config_loader.py</a></td>
                <td class="name left"><a href="config_loader_py.html#t167"><data value='mae_offset'>ConfigLoader.mae_offset</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_loader_py.html#t172">config_loader.py</a></td>
                <td class="name left"><a href="config_loader_py.html#t172"><data value='mae_smoothing_line'>ConfigLoader.mae_smoothing_line</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_loader_py.html#t177">config_loader.py</a></td>
                <td class="name left"><a href="config_loader_py.html#t177"><data value='mae_smoothing_length'>ConfigLoader.mae_smoothing_length</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_loader_py.html#t182">config_loader.py</a></td>
                <td class="name left"><a href="config_loader_py.html#t182"><data value='mae_smoothing_enabled'>ConfigLoader.mae_smoothing_enabled</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_loader_py.html#t188">config_loader.py</a></td>
                <td class="name left"><a href="config_loader_py.html#t188"><data value='pivot_point_enabled'>ConfigLoader.pivot_point_enabled</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_loader_py.html#t193">config_loader.py</a></td>
                <td class="name left"><a href="config_loader_py.html#t193"><data value='pivot_point_calculation_type'>ConfigLoader.pivot_point_calculation_type</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_loader_py.html#t198">config_loader.py</a></td>
                <td class="name left"><a href="config_loader_py.html#t198"><data value='pivot_point_top_n_closest'>ConfigLoader.pivot_point_top_n_closest</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_loader_py.html#t203">config_loader.py</a></td>
                <td class="name left"><a href="config_loader_py.html#t203"><data value='report_filter_top_n_closest'>ConfigLoader.report_filter_top_n_closest</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_loader_py.html#t209">config_loader.py</a></td>
                <td class="name left"><a href="config_loader_py.html#t209"><data value='min_delta'>ConfigLoader.min_delta</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_loader_py.html#t214">config_loader.py</a></td>
                <td class="name left"><a href="config_loader_py.html#t214"><data value='max_delta'>ConfigLoader.max_delta</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_loader_py.html#t219">config_loader.py</a></td>
                <td class="name left"><a href="config_loader_py.html#t219"><data value='ce_pe_pairing_enabled'>ConfigLoader.ce_pe_pairing_enabled</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_loader_py.html#t224">config_loader.py</a></td>
                <td class="name left"><a href="config_loader_py.html#t224"><data value='ce_pe_min_price_percent'>ConfigLoader.ce_pe_min_price_percent</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_loader_py.html#t229">config_loader.py</a></td>
                <td class="name left"><a href="config_loader_py.html#t229"><data value='ce_pe_max_price_percent'>ConfigLoader.ce_pe_max_price_percent</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_loader_py.html#t235">config_loader.py</a></td>
                <td class="name left"><a href="config_loader_py.html#t235"><data value='rate_limit_min_delay_seconds'>ConfigLoader.rate_limit_min_delay_seconds</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_loader_py.html#t240">config_loader.py</a></td>
                <td class="name left"><a href="config_loader_py.html#t240"><data value='rate_limit_max_retries'>ConfigLoader.rate_limit_max_retries</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_loader_py.html#t245">config_loader.py</a></td>
                <td class="name left"><a href="config_loader_py.html#t245"><data value='rate_limit_retry_backoff'>ConfigLoader.rate_limit_retry_backoff</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_loader_py.html#t249">config_loader.py</a></td>
                <td class="name left"><a href="config_loader_py.html#t249"><data value='get_symbol_config'>ConfigLoader.get_symbol_config</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_loader_py.html#t273">config_loader.py</a></td>
                <td class="name left"><a href="config_loader_py.html#t273"><data value='validate_config'>ConfigLoader.validate_config</data></a></td>
                <td>44</td>
                <td>38</td>
                <td>0</td>
                <td class="right" data-ratio="6 44">14%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_loader_py.html#t345">config_loader.py</a></td>
                <td class="name left"><a href="config_loader_py.html#t345"><data value='get_csv_file_for_market_type'>ConfigLoader.get_csv_file_for_market_type</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_loader_py.html#t362">config_loader.py</a></td>
                <td class="name left"><a href="config_loader_py.html#t362"><data value='get_market_types_for_csv'>ConfigLoader.get_market_types_for_csv</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_loader_py.html#t379">config_loader.py</a></td>
                <td class="name left"><a href="config_loader_py.html#t379"><data value='get_enabled_market_types'>ConfigLoader.get_enabled_market_types</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_loader_py.html#t394">config_loader.py</a></td>
                <td class="name left"><a href="config_loader_py.html#t394"><data value='get_config'>get_config</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_loader_py.html#t412">config_loader.py</a></td>
                <td class="name left"><a href="config_loader_py.html#t412"><data value='reload_config'>reload_config</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_loader_py.html">config_loader.py</a></td>
                <td class="name left"><a href="config_loader_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>84</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="84 84">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="constant_py.html">constant.py</a></td>
                <td class="name left"><a href="constant_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="debug_nifty_weekly_py.html#t23">debug_nifty_weekly.py</a></td>
                <td class="name left"><a href="debug_nifty_weekly_py.html#t23"><data value='test_nifty_weekly_pivot_calculations'>test_nifty_weekly_pivot_calculations</data></a></td>
                <td>26</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="0 26">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="debug_nifty_weekly_py.html#t87">debug_nifty_weekly.py</a></td>
                <td class="name left"><a href="debug_nifty_weekly_py.html#t87"><data value='test_min_positive_pivot_calculation'>test_min_positive_pivot_calculation</data></a></td>
                <td>32</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="0 32">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="debug_nifty_weekly_py.html#t135">debug_nifty_weekly.py</a></td>
                <td class="name left"><a href="debug_nifty_weekly_py.html#t135"><data value='test_filtered_symbol_pivot_data_transfer'>test_filtered_symbol_pivot_data_transfer</data></a></td>
                <td>34</td>
                <td>34</td>
                <td>0</td>
                <td class="right" data-ratio="0 34">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="debug_nifty_weekly_py.html#t221">debug_nifty_weekly.py</a></td>
                <td class="name left"><a href="debug_nifty_weekly_py.html#t221"><data value='test_weekly_symbol_parsing_patterns'>test_weekly_symbol_parsing_patterns</data></a></td>
                <td>29</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="0 29">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="debug_nifty_weekly_py.html">debug_nifty_weekly.py</a></td>
                <td class="name left"><a href="debug_nifty_weekly_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="fyers_client_py.html#t56">fyers_client.py</a></td>
                <td class="name left"><a href="fyers_client_py.html#t56"><data value='init__'>FyersClient.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="fyers_client_py.html#t67">fyers_client.py</a></td>
                <td class="name left"><a href="fyers_client_py.html#t67"><data value='authenticate'>FyersClient.authenticate</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="fyers_client_py.html#t103">fyers_client.py</a></td>
                <td class="name left"><a href="fyers_client_py.html#t103"><data value='get_quotes'>FyersClient.get_quotes</data></a></td>
                <td>92</td>
                <td>92</td>
                <td>0</td>
                <td class="right" data-ratio="0 92">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="fyers_client_py.html#t258">fyers_client.py</a></td>
                <td class="name left"><a href="fyers_client_py.html#t258"><data value='process_quotes_data'>FyersClient._process_quotes_data</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="fyers_client_py.html#t296">fyers_client.py</a></td>
                <td class="name left"><a href="fyers_client_py.html#t296"><data value='parse_market_data'>FyersClient._parse_market_data</data></a></td>
                <td>57</td>
                <td>57</td>
                <td>0</td>
                <td class="right" data-ratio="0 57">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="fyers_client_py.html#t404">fyers_client.py</a></td>
                <td class="name left"><a href="fyers_client_py.html#t404"><data value='get_single_quote'>FyersClient.get_single_quote</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="fyers_client_py.html#t417">fyers_client.py</a></td>
                <td class="name left"><a href="fyers_client_py.html#t417"><data value='get_market_data'>FyersClient.get_market_data</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="fyers_client_py.html#t429">fyers_client.py</a></td>
                <td class="name left"><a href="fyers_client_py.html#t429"><data value='get_quotes_optimized'>FyersClient.get_quotes_optimized</data></a></td>
                <td>26</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="0 26">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="fyers_client_py.html#t484">fyers_client.py</a></td>
                <td class="name left"><a href="fyers_client_py.html#t484"><data value='is_authenticated'>FyersClient.is_authenticated</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="fyers_client_py.html#t493">fyers_client.py</a></td>
                <td class="name left"><a href="fyers_client_py.html#t493"><data value='get_historical_data'>FyersClient.get_historical_data</data></a></td>
                <td>42</td>
                <td>42</td>
                <td>0</td>
                <td class="right" data-ratio="0 42">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="fyers_client_py.html">fyers_client.py</a></td>
                <td class="name left"><a href="fyers_client_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>41</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="41 41">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="fyers_config_py.html#t17">fyers_config.py</a></td>
                <td class="name left"><a href="fyers_config_py.html#t17"><data value='setup_logging'>setup_logging</data></a></td>
                <td>43</td>
                <td>43</td>
                <td>0</td>
                <td class="right" data-ratio="0 43">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="fyers_config_py.html#t108">fyers_config.py</a></td>
                <td class="name left"><a href="fyers_config_py.html#t108"><data value='load_environment'>load_environment</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="fyers_config_py.html#t148">fyers_config.py</a></td>
                <td class="name left"><a href="fyers_config_py.html#t148"><data value='init__'>FyersConfig.__init__</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="fyers_config_py.html#t170">fyers_config.py</a></td>
                <td class="name left"><a href="fyers_config_py.html#t170"><data value='authenticate'>FyersConfig.authenticate</data></a></td>
                <td>29</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="0 29">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="fyers_config_py.html#t227">fyers_config.py</a></td>
                <td class="name left"><a href="fyers_config_py.html#t227"><data value='open_browser'>FyersConfig._open_browser</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="fyers_config_py.html#t239">fyers_config.py</a></td>
                <td class="name left"><a href="fyers_config_py.html#t239"><data value='open_windows_browser'>FyersConfig._open_windows_browser</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="fyers_config_py.html#t254">fyers_config.py</a></td>
                <td class="name left"><a href="fyers_config_py.html#t254"><data value='open_unix_browser'>FyersConfig._open_unix_browser</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="fyers_config_py.html#t264">fyers_config.py</a></td>
                <td class="name left"><a href="fyers_config_py.html#t264"><data value='get_auth_code'>FyersConfig._get_auth_code</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="fyers_config_py.html#t280">fyers_config.py</a></td>
                <td class="name left"><a href="fyers_config_py.html#t280"><data value='save_tokens'>FyersConfig._save_tokens</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="fyers_config_py.html#t289">fyers_config.py</a></td>
                <td class="name left"><a href="fyers_config_py.html#t289"><data value='verify_connection'>FyersConfig._verify_connection</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="fyers_config_py.html">fyers_config.py</a></td>
                <td class="name left"><a href="fyers_config_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>25</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="25 25">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="fyers_connect_py.html#t35">fyers_connect.py</a></td>
                <td class="name left"><a href="fyers_connect_py.html#t35"><data value='init__'>FyersConnect.__init__</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="fyers_connect_py.html#t78">fyers_connect.py</a></td>
                <td class="name left"><a href="fyers_connect_py.html#t78"><data value='cleanup_expired_cache'>FyersConnect._cleanup_expired_cache</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="fyers_connect_py.html#t105">fyers_connect.py</a></td>
                <td class="name left"><a href="fyers_connect_py.html#t105"><data value='load_index_symbols'>FyersConnect._load_index_symbols</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="fyers_connect_py.html#t133">fyers_connect.py</a></td>
                <td class="name left"><a href="fyers_connect_py.html#t133"><data value='get_dynamic_index_symbol_mapping'>FyersConnect.get_dynamic_index_symbol_mapping</data></a></td>
                <td>26</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="0 26">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="fyers_connect_py.html#t182">fyers_connect.py</a></td>
                <td class="name left"><a href="fyers_connect_py.html#t182"><data value='login'>FyersConnect.login</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="fyers_connect_py.html#t223">fyers_connect.py</a></td>
                <td class="name left"><a href="fyers_connect_py.html#t223"><data value='get_ohlc_data'>FyersConnect.get_ohlc_data</data></a></td>
                <td>44</td>
                <td>44</td>
                <td>0</td>
                <td class="right" data-ratio="0 44">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="fyers_connect_py.html#t315">fyers_connect.py</a></td>
                <td class="name left"><a href="fyers_connect_py.html#t315"><data value='get_weekly_pivot_ohlc_data'>FyersConnect.get_weekly_pivot_ohlc_data</data></a></td>
                <td>34</td>
                <td>34</td>
                <td>0</td>
                <td class="right" data-ratio="0 34">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="fyers_connect_py.html#t396">fyers_connect.py</a></td>
                <td class="name left"><a href="fyers_connect_py.html#t396"><data value='fetch_weekly_ohlc_for_option'>FyersConnect._fetch_weekly_ohlc_for_option</data></a></td>
                <td>38</td>
                <td>38</td>
                <td>0</td>
                <td class="right" data-ratio="0 38">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="fyers_connect_py.html#t485">fyers_connect.py</a></td>
                <td class="name left"><a href="fyers_connect_py.html#t485"><data value='fetch_daily_ohlc_data_for_range'>FyersConnect._fetch_daily_ohlc_data_for_range</data></a></td>
                <td>48</td>
                <td>48</td>
                <td>0</td>
                <td class="right" data-ratio="0 48">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="fyers_connect_py.html#t574">fyers_connect.py</a></td>
                <td class="name left"><a href="fyers_connect_py.html#t574"><data value='get_option_chain'>FyersConnect.get_option_chain</data></a></td>
                <td>201</td>
                <td>201</td>
                <td>0</td>
                <td class="right" data-ratio="0 201">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="fyers_connect_py.html#t948">fyers_connect.py</a></td>
                <td class="name left"><a href="fyers_connect_py.html#t948"><data value='is_weekly_expiry'>FyersConnect._is_weekly_expiry</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="fyers_connect_py.html#t966">fyers_connect.py</a></td>
                <td class="name left"><a href="fyers_connect_py.html#t966"><data value='get_exchange_for_symbol'>FyersConnect._get_exchange_for_symbol</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="fyers_connect_py.html#t974">fyers_connect.py</a></td>
                <td class="name left"><a href="fyers_connect_py.html#t974"><data value='is_futures_symbol'>FyersConnect._is_futures_symbol</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="fyers_connect_py.html#t984">fyers_connect.py</a></td>
                <td class="name left"><a href="fyers_connect_py.html#t984"><data value='format_symbol'>FyersConnect._format_symbol</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="fyers_connect_py.html#t1025">fyers_connect.py</a></td>
                <td class="name left"><a href="fyers_connect_py.html#t1025"><data value='format_batch_symbols'>FyersConnect._format_batch_symbols</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="fyers_connect_py.html#t1035">fyers_connect.py</a></td>
                <td class="name left"><a href="fyers_connect_py.html#t1035"><data value='calculate_time_to_expiry'>FyersConnect._calculate_time_to_expiry</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="fyers_connect_py.html#t1048">fyers_connect.py</a></td>
                <td class="name left"><a href="fyers_connect_py.html#t1048"><data value='get_quote_data'>FyersConnect._get_quote_data</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="fyers_connect_py.html#t1066">fyers_connect.py</a></td>
                <td class="name left"><a href="fyers_connect_py.html#t1066"><data value='get_spot_price'>FyersConnect.get_spot_price</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="fyers_connect_py.html#t1095">fyers_connect.py</a></td>
                <td class="name left"><a href="fyers_connect_py.html#t1095"><data value='clear_spot_price_cache'>FyersConnect.clear_spot_price_cache</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="fyers_connect_py.html#t1101">fyers_connect.py</a></td>
                <td class="name left"><a href="fyers_connect_py.html#t1101"><data value='get_cached_spot_prices'>FyersConnect.get_cached_spot_prices</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="fyers_connect_py.html#t1105">fyers_connect.py</a></td>
                <td class="name left"><a href="fyers_connect_py.html#t1105"><data value='reset_failed_symbols'>FyersConnect.reset_failed_symbols</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="fyers_connect_py.html#t1111">fyers_connect.py</a></td>
                <td class="name left"><a href="fyers_connect_py.html#t1111"><data value='get_failed_symbols'>FyersConnect.get_failed_symbols</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="fyers_connect_py.html">fyers_connect.py</a></td>
                <td class="name left"><a href="fyers_connect_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>37</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="37 37">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="main_py.html#t17">main.py</a></td>
                <td class="name left"><a href="main_py.html#t17"><data value='main'>main</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="main_py.html#t51">main.py</a></td>
                <td class="name left"><a href="main_py.html#t51"><data value='print_banner'>print_banner</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="main_py.html#t66">main.py</a></td>
                <td class="name left"><a href="main_py.html#t66"><data value='check_prerequisites'>check_prerequisites</data></a></td>
                <td>24</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="0 24">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="main_py.html">main.py</a></td>
                <td class="name left"><a href="main_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="market_type_scanner_py.html#t55">market_type_scanner.py</a></td>
                <td class="name left"><a href="market_type_scanner_py.html#t55"><data value='init__'>BaseMarketScanner.__init__</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="market_type_scanner_py.html#t76">market_type_scanner.py</a></td>
                <td class="name left"><a href="market_type_scanner_py.html#t76"><data value='authenticate_fyers'>BaseMarketScanner.authenticate_fyers</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="market_type_scanner_py.html#t91">market_type_scanner.py</a></td>
                <td class="name left"><a href="market_type_scanner_py.html#t91"><data value='get_symbols_for_scanning'>BaseMarketScanner.get_symbols_for_scanning</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="market_type_scanner_py.html#t97">market_type_scanner.py</a></td>
                <td class="name left"><a href="market_type_scanner_py.html#t97"><data value='fetch_market_data'>BaseMarketScanner.fetch_market_data</data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="market_type_scanner_py.html#t140">market_type_scanner.py</a></td>
                <td class="name left"><a href="market_type_scanner_py.html#t140"><data value='optimize_memory_usage'>BaseMarketScanner._optimize_memory_usage</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="market_type_scanner_py.html#t185">market_type_scanner.py</a></td>
                <td class="name left"><a href="market_type_scanner_py.html#t185"><data value='apply_volume_filter'>BaseMarketScanner.apply_volume_filter</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="market_type_scanner_py.html#t201">market_type_scanner.py</a></td>
                <td class="name left"><a href="market_type_scanner_py.html#t201"><data value='apply_ltp_filter'>BaseMarketScanner.apply_ltp_filter</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="market_type_scanner_py.html#t212">market_type_scanner.py</a></td>
                <td class="name left"><a href="market_type_scanner_py.html#t212"><data value='update_ohlc_with_timeframe_data'>BaseMarketScanner.update_ohlc_with_timeframe_data</data></a></td>
                <td>28</td>
                <td>28</td>
                <td>0</td>
                <td class="right" data-ratio="0 28">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="market_type_scanner_py.html#t261">market_type_scanner.py</a></td>
                <td class="name left"><a href="market_type_scanner_py.html#t261"><data value='apply_market_specific_filters'>BaseMarketScanner.apply_market_specific_filters</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="market_type_scanner_py.html#t265">market_type_scanner.py</a></td>
                <td class="name left"><a href="market_type_scanner_py.html#t265"><data value='apply_mae_filter'>BaseMarketScanner.apply_mae_filter</data></a></td>
                <td>34</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="29 34">85%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="market_type_scanner_py.html#t336">market_type_scanner.py</a></td>
                <td class="name left"><a href="market_type_scanner_py.html#t336"><data value='apply_pivot_point_filter'>BaseMarketScanner.apply_pivot_point_filter</data></a></td>
                <td>35</td>
                <td>35</td>
                <td>0</td>
                <td class="right" data-ratio="0 35">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="market_type_scanner_py.html#t392">market_type_scanner.py</a></td>
                <td class="name left"><a href="market_type_scanner_py.html#t392"><data value='convert_to_filtered_symbols'>BaseMarketScanner.convert_to_filtered_symbols</data></a></td>
                <td>60</td>
                <td>60</td>
                <td>0</td>
                <td class="right" data-ratio="0 60">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="market_type_scanner_py.html#t502">market_type_scanner.py</a></td>
                <td class="name left"><a href="market_type_scanner_py.html#t502"><data value='parse_symbol_for_pivot_mode'>BaseMarketScanner._parse_symbol_for_pivot_mode</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="market_type_scanner_py.html#t545">market_type_scanner.py</a></td>
                <td class="name left"><a href="market_type_scanner_py.html#t545"><data value='scan_symbols'>BaseMarketScanner.scan_symbols</data></a></td>
                <td>34</td>
                <td>34</td>
                <td>0</td>
                <td class="right" data-ratio="0 34">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="market_type_scanner_py.html#t608">market_type_scanner.py</a></td>
                <td class="name left"><a href="market_type_scanner_py.html#t608"><data value='init__'>EquityScanner.__init__</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="market_type_scanner_py.html#t611">market_type_scanner.py</a></td>
                <td class="name left"><a href="market_type_scanner_py.html#t611"><data value='scan_symbols'>EquityScanner.scan_symbols</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="market_type_scanner_py.html#t632">market_type_scanner.py</a></td>
                <td class="name left"><a href="market_type_scanner_py.html#t632"><data value='scan_equity_with_pivot_point_flow'>EquityScanner._scan_equity_with_pivot_point_flow</data></a></td>
                <td>27</td>
                <td>27</td>
                <td>0</td>
                <td class="right" data-ratio="0 27">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="market_type_scanner_py.html#t681">market_type_scanner.py</a></td>
                <td class="name left"><a href="market_type_scanner_py.html#t681"><data value='scan_equity_original_flow'>EquityScanner._scan_equity_original_flow</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="market_type_scanner_py.html#t686">market_type_scanner.py</a></td>
                <td class="name left"><a href="market_type_scanner_py.html#t686"><data value='apply_market_specific_filters'>EquityScanner.apply_market_specific_filters</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="market_type_scanner_py.html#t700">market_type_scanner.py</a></td>
                <td class="name left"><a href="market_type_scanner_py.html#t700"><data value='init__'>IndexScanner.__init__</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="market_type_scanner_py.html#t703">market_type_scanner.py</a></td>
                <td class="name left"><a href="market_type_scanner_py.html#t703"><data value='scan_symbols'>IndexScanner.scan_symbols</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="market_type_scanner_py.html#t724">market_type_scanner.py</a></td>
                <td class="name left"><a href="market_type_scanner_py.html#t724"><data value='scan_index_with_pivot_point_flow'>IndexScanner._scan_index_with_pivot_point_flow</data></a></td>
                <td>27</td>
                <td>27</td>
                <td>0</td>
                <td class="right" data-ratio="0 27">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="market_type_scanner_py.html#t773">market_type_scanner.py</a></td>
                <td class="name left"><a href="market_type_scanner_py.html#t773"><data value='scan_index_original_flow'>IndexScanner._scan_index_original_flow</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="market_type_scanner_py.html#t778">market_type_scanner.py</a></td>
                <td class="name left"><a href="market_type_scanner_py.html#t778"><data value='apply_market_specific_filters'>IndexScanner.apply_market_specific_filters</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="market_type_scanner_py.html#t792">market_type_scanner.py</a></td>
                <td class="name left"><a href="market_type_scanner_py.html#t792"><data value='init__'>FuturesScanner.__init__</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="market_type_scanner_py.html#t795">market_type_scanner.py</a></td>
                <td class="name left"><a href="market_type_scanner_py.html#t795"><data value='scan_symbols'>FuturesScanner.scan_symbols</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="market_type_scanner_py.html#t816">market_type_scanner.py</a></td>
                <td class="name left"><a href="market_type_scanner_py.html#t816"><data value='scan_futures_with_pivot_point_flow'>FuturesScanner._scan_futures_with_pivot_point_flow</data></a></td>
                <td>27</td>
                <td>27</td>
                <td>0</td>
                <td class="right" data-ratio="0 27">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="market_type_scanner_py.html#t865">market_type_scanner.py</a></td>
                <td class="name left"><a href="market_type_scanner_py.html#t865"><data value='scan_futures_original_flow'>FuturesScanner._scan_futures_original_flow</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="market_type_scanner_py.html#t870">market_type_scanner.py</a></td>
                <td class="name left"><a href="market_type_scanner_py.html#t870"><data value='apply_market_specific_filters'>FuturesScanner.apply_market_specific_filters</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="market_type_scanner_py.html#t884">market_type_scanner.py</a></td>
                <td class="name left"><a href="market_type_scanner_py.html#t884"><data value='init__'>OptionsScanner.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="market_type_scanner_py.html#t888">market_type_scanner.py</a></td>
                <td class="name left"><a href="market_type_scanner_py.html#t888"><data value='get_symbols_for_scanning'>OptionsScanner.get_symbols_for_scanning</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="market_type_scanner_py.html#t914">market_type_scanner.py</a></td>
                <td class="name left"><a href="market_type_scanner_py.html#t914"><data value='get_symbols_for_pivot_point_scanning'>OptionsScanner._get_symbols_for_pivot_point_scanning</data></a></td>
                <td>45</td>
                <td>45</td>
                <td>0</td>
                <td class="right" data-ratio="0 45">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="market_type_scanner_py.html#t997">market_type_scanner.py</a></td>
                <td class="name left"><a href="market_type_scanner_py.html#t997"><data value='apply_delta_based_filtering'>OptionsScanner._apply_delta_based_filtering</data></a></td>
                <td>70</td>
                <td>70</td>
                <td>0</td>
                <td class="right" data-ratio="0 70">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="market_type_scanner_py.html#t1113">market_type_scanner.py</a></td>
                <td class="name left"><a href="market_type_scanner_py.html#t1113"><data value='apply_pre_filtering'>OptionsScanner._apply_pre_filtering</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="market_type_scanner_py.html#t1152">market_type_scanner.py</a></td>
                <td class="name left"><a href="market_type_scanner_py.html#t1152"><data value='apply_market_specific_filters'>OptionsScanner.apply_market_specific_filters</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="market_type_scanner_py.html#t1185">market_type_scanner.py</a></td>
                <td class="name left"><a href="market_type_scanner_py.html#t1185"><data value='apply_ce_pe_pairing_on_filtered_symbols'>OptionsScanner.apply_ce_pe_pairing_on_filtered_symbols</data></a></td>
                <td>43</td>
                <td>43</td>
                <td>0</td>
                <td class="right" data-ratio="0 43">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="market_type_scanner_py.html#t1242">market_type_scanner.py</a></td>
                <td class="name left"><a href="market_type_scanner_py.html#t1242"><data value='scan_symbols'>OptionsScanner.scan_symbols</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="market_type_scanner_py.html#t1263">market_type_scanner.py</a></td>
                <td class="name left"><a href="market_type_scanner_py.html#t1263"><data value='scan_options_with_pivot_point_flow'>OptionsScanner._scan_options_with_pivot_point_flow</data></a></td>
                <td>39</td>
                <td>39</td>
                <td>0</td>
                <td class="right" data-ratio="0 39">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="market_type_scanner_py.html#t1329">market_type_scanner.py</a></td>
                <td class="name left"><a href="market_type_scanner_py.html#t1329"><data value='scan_options_original_flow'>OptionsScanner._scan_options_original_flow</data></a></td>
                <td>31</td>
                <td>31</td>
                <td>0</td>
                <td class="right" data-ratio="0 31">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="market_type_scanner_py.html#t1387">market_type_scanner.py</a></td>
                <td class="name left"><a href="market_type_scanner_py.html#t1387"><data value='create_scanner'>MarketTypeScannerFactory.create_scanner</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="market_type_scanner_py.html#t1410">market_type_scanner.py</a></td>
                <td class="name left"><a href="market_type_scanner_py.html#t1410"><data value='get_supported_market_types'>MarketTypeScannerFactory.get_supported_market_types</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="market_type_scanner_py.html">market_type_scanner.py</a></td>
                <td class="name left"><a href="market_type_scanner_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>86</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="86 86">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="option_utils_py.html#t15">option_utils.py</a></td>
                <td class="name left"><a href="option_utils_py.html#t15"><data value='get_last_thursday_of_month'>get_last_thursday_of_month</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="option_utils_py.html#t38">option_utils.py</a></td>
                <td class="name left"><a href="option_utils_py.html#t38"><data value='get_current_week_expiry'>get_current_week_expiry</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="option_utils_py.html#t63">option_utils.py</a></td>
                <td class="name left"><a href="option_utils_py.html#t63"><data value='get_next_week_expiry'>get_next_week_expiry</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="option_utils_py.html#t74">option_utils.py</a></td>
                <td class="name left"><a href="option_utils_py.html#t74"><data value='is_weekly_expiry_valid'>is_weekly_expiry_valid</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="option_utils_py.html#t103">option_utils.py</a></td>
                <td class="name left"><a href="option_utils_py.html#t103"><data value='format_weekly_option_symbol'>format_weekly_option_symbol</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="option_utils_py.html#t131">option_utils.py</a></td>
                <td class="name left"><a href="option_utils_py.html#t131"><data value='format_monthly_option_symbol'>format_monthly_option_symbol</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="option_utils_py.html#t145">option_utils.py</a></td>
                <td class="name left"><a href="option_utils_py.html#t145"><data value='black_scholes_price'>black_scholes_price</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="option_utils_py.html#t159">option_utils.py</a></td>
                <td class="name left"><a href="option_utils_py.html#t159"><data value='implied_volatility'>implied_volatility</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="option_utils_py.html#t160">option_utils.py</a></td>
                <td class="name left"><a href="option_utils_py.html#t160"><data value='objective'>implied_volatility.objective</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="option_utils_py.html#t172">option_utils.py</a></td>
                <td class="name left"><a href="option_utils_py.html#t172"><data value='black_scholes_delta'>black_scholes_delta</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="option_utils_py.html">option_utils.py</a></td>
                <td class="name left"><a href="option_utils_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="options_chain_filter_py.html#t31">options_chain_filter.py</a></td>
                <td class="name left"><a href="options_chain_filter_py.html#t31"><data value='post_init__'>OptionsChainData.__post_init__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="options_chain_filter_py.html#t41">options_chain_filter.py</a></td>
                <td class="name left"><a href="options_chain_filter_py.html#t41"><data value='init__'>OptionsChainFilter.__init__</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="options_chain_filter_py.html#t64">options_chain_filter.py</a></td>
                <td class="name left"><a href="options_chain_filter_py.html#t64"><data value='get_fyers_client'>OptionsChainFilter.get_fyers_client</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="options_chain_filter_py.html#t79">options_chain_filter.py</a></td>
                <td class="name left"><a href="options_chain_filter_py.html#t79"><data value='load_nse_cm_symbols'>OptionsChainFilter._load_nse_cm_symbols</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="options_chain_filter_py.html#t109">options_chain_filter.py</a></td>
                <td class="name left"><a href="options_chain_filter_py.html#t109"><data value='get_spot_prices_for_underlyings'>OptionsChainFilter.get_spot_prices_for_underlyings</data></a></td>
                <td>48</td>
                <td>48</td>
                <td>0</td>
                <td class="right" data-ratio="0 48">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="options_chain_filter_py.html#t171">options_chain_filter.py</a></td>
                <td class="name left"><a href="options_chain_filter_py.html#t171"><data value='get_index_symbol'>OptionsChainFilter._get_index_symbol</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="options_chain_filter_py.html#t193">options_chain_filter.py</a></td>
                <td class="name left"><a href="options_chain_filter_py.html#t193"><data value='get_strike_interval'>OptionsChainFilter.get_strike_interval</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="options_chain_filter_py.html#t205">options_chain_filter.py</a></td>
                <td class="name left"><a href="options_chain_filter_py.html#t205"><data value='calculate_dynamic_strike_range'>OptionsChainFilter.calculate_dynamic_strike_range</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="options_chain_filter_py.html#t232">options_chain_filter.py</a></td>
                <td class="name left"><a href="options_chain_filter_py.html#t232"><data value='detect_strike_multiplier'>OptionsChainFilter.detect_strike_multiplier</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="options_chain_filter_py.html#t275">options_chain_filter.py</a></td>
                <td class="name left"><a href="options_chain_filter_py.html#t275"><data value='estimate_atm_strike'>OptionsChainFilter.estimate_atm_strike</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="options_chain_filter_py.html#t309">options_chain_filter.py</a></td>
                <td class="name left"><a href="options_chain_filter_py.html#t309"><data value='group_options_by_expiry'>OptionsChainFilter.group_options_by_expiry</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="options_chain_filter_py.html#t328">options_chain_filter.py</a></td>
                <td class="name left"><a href="options_chain_filter_py.html#t328"><data value='get_last_thursday_of_month'>OptionsChainFilter.get_last_thursday_of_month</data></a></td>
                <td>6</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="5 6">83%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="options_chain_filter_py.html#t351">options_chain_filter.py</a></td>
                <td class="name left"><a href="options_chain_filter_py.html#t351"><data value='get_target_months_for_filtering'>OptionsChainFilter.get_target_months_for_filtering</data></a></td>
                <td>33</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="20 33">61%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="options_chain_filter_py.html#t410">options_chain_filter.py</a></td>
                <td class="name left"><a href="options_chain_filter_py.html#t410"><data value='filter_options_by_target_months'>OptionsChainFilter.filter_options_by_target_months</data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="options_chain_filter_py.html#t432">options_chain_filter.py</a></td>
                <td class="name left"><a href="options_chain_filter_py.html#t432"><data value='filter_options_by_weekly_expiry'>OptionsChainFilter.filter_options_by_weekly_expiry</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="options_chain_filter_py.html#t458">options_chain_filter.py</a></td>
                <td class="name left"><a href="options_chain_filter_py.html#t458"><data value='apply_ce_pe_pairing_filter'>OptionsChainFilter.apply_ce_pe_pairing_filter</data></a></td>
                <td>31</td>
                <td>31</td>
                <td>0</td>
                <td class="right" data-ratio="0 31">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="options_chain_filter_py.html#t522">options_chain_filter.py</a></td>
                <td class="name left"><a href="options_chain_filter_py.html#t522"><data value='filter_options_around_atm'>OptionsChainFilter.filter_options_around_atm</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="options_chain_filter_py.html#t560">options_chain_filter.py</a></td>
                <td class="name left"><a href="options_chain_filter_py.html#t560"><data value='filter_options_with_dynamic_range'>OptionsChainFilter.filter_options_with_dynamic_range</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="options_chain_filter_py.html#t595">options_chain_filter.py</a></td>
                <td class="name left"><a href="options_chain_filter_py.html#t595"><data value='create_options_chain'>OptionsChainFilter.create_options_chain</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="options_chain_filter_py.html#t656">options_chain_filter.py</a></td>
                <td class="name left"><a href="options_chain_filter_py.html#t656"><data value='filter_options_symbols'>OptionsChainFilter.filter_options_symbols</data></a></td>
                <td>38</td>
                <td>38</td>
                <td>0</td>
                <td class="right" data-ratio="0 38">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="options_chain_filter_py.html#t734">options_chain_filter.py</a></td>
                <td class="name left"><a href="options_chain_filter_py.html#t734"><data value='pre_filter_options_symbols'>OptionsChainFilter.pre_filter_options_symbols</data></a></td>
                <td>31</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="26 31">84%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="options_chain_filter_py.html#t800">options_chain_filter.py</a></td>
                <td class="name left"><a href="options_chain_filter_py.html#t800"><data value='aggressive_filter_by_spot_price'>OptionsChainFilter._aggressive_filter_by_spot_price</data></a></td>
                <td>28</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="26 28">93%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="options_chain_filter_py.html#t854">options_chain_filter.py</a></td>
                <td class="name left"><a href="options_chain_filter_py.html#t854"><data value='fallback_aggressive_filter'>OptionsChainFilter._fallback_aggressive_filter</data></a></td>
                <td>18</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="16 18">89%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="options_chain_filter_py.html#t887">options_chain_filter.py</a></td>
                <td class="name left"><a href="options_chain_filter_py.html#t887"><data value='get_options_summary'>OptionsChainFilter.get_options_summary</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="options_chain_filter_py.html">options_chain_filter.py</a></td>
                <td class="name left"><a href="options_chain_filter_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>45</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="45 45">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="pivot_point_core_py.html#t14">pivot_point_core.py</a></td>
                <td class="name left"><a href="pivot_point_core_py.html#t14"><data value='create_reports_directory'>create_reports_directory</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="pivot_point_core_py.html#t23">pivot_point_core.py</a></td>
                <td class="name left"><a href="pivot_point_core_py.html#t23"><data value='generate_timestamped_filename'>generate_timestamped_filename</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="pivot_point_core_py.html#t28">pivot_point_core.py</a></td>
                <td class="name left"><a href="pivot_point_core_py.html#t28"><data value='get_filtered_options'>get_filtered_options</data></a></td>
                <td>40</td>
                <td>40</td>
                <td>0</td>
                <td class="right" data-ratio="0 40">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="pivot_point_core_py.html#t91">pivot_point_core.py</a></td>
                <td class="name left"><a href="pivot_point_core_py.html#t91"><data value='calculate_pivot_points'>calculate_pivot_points</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="pivot_point_core_py.html#t117">pivot_point_core.py</a></td>
                <td class="name left"><a href="pivot_point_core_py.html#t117"><data value='calculate_row_pivots'>calculate_pivot_points.calculate_row_pivots</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="pivot_point_core_py.html#t131">pivot_point_core.py</a></td>
                <td class="name left"><a href="pivot_point_core_py.html#t131"><data value='find_closest_to_positive_pivots'>find_closest_to_positive_pivots</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="pivot_point_core_py.html#t136">pivot_point_core.py</a></td>
                <td class="name left"><a href="pivot_point_core_py.html#t136"><data value='get_closest_positive_pivot_info'>find_closest_to_positive_pivots.get_closest_positive_pivot_info</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="pivot_point_core_py.html#t172">pivot_point_core.py</a></td>
                <td class="name left"><a href="pivot_point_core_py.html#t172"><data value='find_best_match_to_min_positive_pivot'>find_best_match_to_min_positive_pivot</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="pivot_point_core_py.html#t180">pivot_point_core.py</a></td>
                <td class="name left"><a href="pivot_point_core_py.html#t180"><data value='get_min_positive_pivot_info'>find_best_match_to_min_positive_pivot.get_min_positive_pivot_info</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="pivot_point_core_py.html#t211">pivot_point_core.py</a></td>
                <td class="name left"><a href="pivot_point_core_py.html#t211"><data value='find_options_closest_to_min_positive_pivot'>find_options_closest_to_min_positive_pivot</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="pivot_point_core_py.html#t218">pivot_point_core.py</a></td>
                <td class="name left"><a href="pivot_point_core_py.html#t218"><data value='get_min_positive_pivot_info'>find_options_closest_to_min_positive_pivot.get_min_positive_pivot_info</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="pivot_point_core_py.html#t272">pivot_point_core.py</a></td>
                <td class="name left"><a href="pivot_point_core_py.html#t272"><data value='main'>main</data></a></td>
                <td>96</td>
                <td>96</td>
                <td>0</td>
                <td class="right" data-ratio="0 96">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="pivot_point_core_py.html">pivot_point_core.py</a></td>
                <td class="name left"><a href="pivot_point_core_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="pivot_point_integration_py.html#t40">pivot_point_integration.py</a></td>
                <td class="name left"><a href="pivot_point_integration_py.html#t40"><data value='init__'>PivotPointIntegration.__init__</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="pivot_point_integration_py.html#t63">pivot_point_integration.py</a></td>
                <td class="name left"><a href="pivot_point_integration_py.html#t63"><data value='is_enabled'>PivotPointIntegration.is_enabled</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="pivot_point_integration_py.html#t67">pivot_point_integration.py</a></td>
                <td class="name left"><a href="pivot_point_integration_py.html#t67"><data value='calculate_pivot_points_for_symbol'>PivotPointIntegration.calculate_pivot_points_for_symbol</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="pivot_point_integration_py.html#t93">pivot_point_integration.py</a></td>
                <td class="name left"><a href="pivot_point_integration_py.html#t93"><data value='is_options_symbol'>PivotPointIntegration._is_options_symbol</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="pivot_point_integration_py.html#t97">pivot_point_integration.py</a></td>
                <td class="name left"><a href="pivot_point_integration_py.html#t97"><data value='calculate_pivot_points_for_option_with_ohlc'>PivotPointIntegration._calculate_pivot_points_for_option_with_ohlc</data></a></td>
                <td>66</td>
                <td>66</td>
                <td>0</td>
                <td class="right" data-ratio="0 66">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="pivot_point_integration_py.html#t212">pivot_point_integration.py</a></td>
                <td class="name left"><a href="pivot_point_integration_py.html#t212"><data value='calculate_pivot_points_for_non_option_with_ohlc'>PivotPointIntegration._calculate_pivot_points_for_non_option_with_ohlc</data></a></td>
                <td>29</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="0 29">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="pivot_point_integration_py.html#t263">pivot_point_integration.py</a></td>
                <td class="name left"><a href="pivot_point_integration_py.html#t263"><data value='fetch_ohlc_for_non_option'>PivotPointIntegration._fetch_ohlc_for_non_option</data></a></td>
                <td>46</td>
                <td>46</td>
                <td>0</td>
                <td class="right" data-ratio="0 46">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="pivot_point_integration_py.html#t342">pivot_point_integration.py</a></td>
                <td class="name left"><a href="pivot_point_integration_py.html#t342"><data value='get_fyers_connect'>PivotPointIntegration._get_fyers_connect</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="pivot_point_integration_py.html#t367">pivot_point_integration.py</a></td>
                <td class="name left"><a href="pivot_point_integration_py.html#t367"><data value='fetch_ohlc_for_option'>PivotPointIntegration._fetch_ohlc_for_option</data></a></td>
                <td>59</td>
                <td>59</td>
                <td>0</td>
                <td class="right" data-ratio="0 59">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="pivot_point_integration_py.html#t456">pivot_point_integration.py</a></td>
                <td class="name left"><a href="pivot_point_integration_py.html#t456"><data value='calculate_pivot_points_for_options'>PivotPointIntegration.calculate_pivot_points_for_options</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="pivot_point_integration_py.html#t495">pivot_point_integration.py</a></td>
                <td class="name left"><a href="pivot_point_integration_py.html#t495"><data value='calculate_closest_positive_pivot'>PivotPointIntegration._calculate_closest_positive_pivot</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="pivot_point_integration_py.html#t520">pivot_point_integration.py</a></td>
                <td class="name left"><a href="pivot_point_integration_py.html#t520"><data value='calculate_min_positive_pivot'>PivotPointIntegration._calculate_min_positive_pivot</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="pivot_point_integration_py.html#t558">pivot_point_integration.py</a></td>
                <td class="name left"><a href="pivot_point_integration_py.html#t558"><data value='add_pivot_columns_to_dataframe'>PivotPointIntegration.add_pivot_columns_to_dataframe</data></a></td>
                <td>30</td>
                <td>30</td>
                <td>0</td>
                <td class="right" data-ratio="0 30">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="pivot_point_integration_py.html#t615">pivot_point_integration.py</a></td>
                <td class="name left"><a href="pivot_point_integration_py.html#t615"><data value='filter_by_closest_pivot_points'>PivotPointIntegration.filter_by_closest_pivot_points</data></a></td>
                <td>26</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="0 26">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="pivot_point_integration_py.html">pivot_point_integration.py</a></td>
                <td class="name left"><a href="pivot_point_integration_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>36</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="35 36">97%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="pivot_points_py.html#t14">pivot_points.py</a></td>
                <td class="name left"><a href="pivot_points_py.html#t14"><data value='calculate_pivot_standard'>_calculate_pivot_standard</data></a></td>
                <td>26</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="0 26">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="pivot_points_py.html#t28">pivot_points.py</a></td>
                <td class="name left"><a href="pivot_points_py.html#t28"><data value='align_to_nearest_5'>_calculate_pivot_standard._align_to_nearest_5</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="pivot_points_py.html#t105">pivot_points.py</a></td>
                <td class="name left"><a href="pivot_points_py.html#t105"><data value='calculate_pivot_points'>calculate_pivot_points</data></a></td>
                <td>41</td>
                <td>41</td>
                <td>0</td>
                <td class="right" data-ratio="0 41">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="pivot_points_py.html">pivot_points.py</a></td>
                <td class="name left"><a href="pivot_points_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="report_generator_py.html#t22">report_generator.py</a></td>
                <td class="name left"><a href="report_generator_py.html#t22"><data value='init__'>ReportGenerator.__init__</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="report_generator_py.html#t44">report_generator.py</a></td>
                <td class="name left"><a href="report_generator_py.html#t44"><data value='create_underlying_pattern'>ReportGenerator._create_underlying_pattern</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="report_generator_py.html#t60">report_generator.py</a></td>
                <td class="name left"><a href="report_generator_py.html#t60"><data value='ensure_output_directory'>ReportGenerator.ensure_output_directory</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="report_generator_py.html#t69">report_generator.py</a></td>
                <td class="name left"><a href="report_generator_py.html#t69"><data value='extract_underlying_from_symbol'>ReportGenerator.extract_underlying_from_symbol</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="report_generator_py.html#t95">report_generator.py</a></td>
                <td class="name left"><a href="report_generator_py.html#t95"><data value='extract_month_from_symbol'>ReportGenerator.extract_month_from_symbol</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="report_generator_py.html#t125">report_generator.py</a></td>
                <td class="name left"><a href="report_generator_py.html#t125"><data value='sort_symbols_by_underlying_and_month'>ReportGenerator.sort_symbols_by_underlying_and_month</data></a></td>
                <td>34</td>
                <td>34</td>
                <td>0</td>
                <td class="right" data-ratio="0 34">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="report_generator_py.html#t209">report_generator.py</a></td>
                <td class="name left"><a href="report_generator_py.html#t209"><data value='generate_filename'>ReportGenerator.generate_filename</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="report_generator_py.html#t223">report_generator.py</a></td>
                <td class="name left"><a href="report_generator_py.html#t223"><data value='create_csv_report'>ReportGenerator.create_csv_report</data></a></td>
                <td>78</td>
                <td>78</td>
                <td>0</td>
                <td class="right" data-ratio="0 78">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="report_generator_py.html#t409">report_generator.py</a></td>
                <td class="name left"><a href="report_generator_py.html#t409"><data value='apply_top_n_closest_filtering'>ReportGenerator._apply_top_n_closest_filtering</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="report_generator_py.html#t457">report_generator.py</a></td>
                <td class="name left"><a href="report_generator_py.html#t457"><data value='create_summary_report'>ReportGenerator.create_summary_report</data></a></td>
                <td>79</td>
                <td>79</td>
                <td>0</td>
                <td class="right" data-ratio="0 79">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="report_generator_py.html#t579">report_generator.py</a></td>
                <td class="name left"><a href="report_generator_py.html#t579"><data value='print_console_summary'>ReportGenerator.print_console_summary</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="report_generator_py.html#t612">report_generator.py</a></td>
                <td class="name left"><a href="report_generator_py.html#t612"><data value='generate_market_type_report'>ReportGenerator.generate_market_type_report</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="report_generator_py.html#t647">report_generator.py</a></td>
                <td class="name left"><a href="report_generator_py.html#t647"><data value='generate_full_report'>ReportGenerator.generate_full_report</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="report_generator_py.html#t678">report_generator.py</a></td>
                <td class="name left"><a href="report_generator_py.html#t678"><data value='format_expiry_date_for_pivot_mode'>ReportGenerator._format_expiry_date_for_pivot_mode</data></a></td>
                <td>36</td>
                <td>36</td>
                <td>0</td>
                <td class="right" data-ratio="0 36">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="report_generator_py.html">report_generator.py</a></td>
                <td class="name left"><a href="report_generator_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>25</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="0 25">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="symbol_config_py.html#t94">symbol_config.py</a></td>
                <td class="name left"><a href="symbol_config_py.html#t94"><data value='get_dynamic_symbol_config'>get_dynamic_symbol_config</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="symbol_config_py.html#t128">symbol_config.py</a></td>
                <td class="name left"><a href="symbol_config_py.html#t128"><data value='get_symbol_config'>get_symbol_config</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="symbol_config_py.html">symbol_config.py</a></td>
                <td class="name left"><a href="symbol_config_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="symbol_downloader_py.html#t11">symbol_downloader.py</a></td>
                <td class="name left"><a href="symbol_downloader_py.html#t11"><data value='get_config'>get_config</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="symbol_downloader_py.html#t16">symbol_downloader.py</a></td>
                <td class="name left"><a href="symbol_downloader_py.html#t16"><data value='download_file'>download_file</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="symbol_downloader_py.html#t45">symbol_downloader.py</a></td>
                <td class="name left"><a href="symbol_downloader_py.html#t45"><data value='determine_csv_filename'>determine_csv_filename</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="symbol_downloader_py.html#t72">symbol_downloader.py</a></td>
                <td class="name left"><a href="symbol_downloader_py.html#t72"><data value='backup_existing_file'>backup_existing_file</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="symbol_downloader_py.html#t99">symbol_downloader.py</a></td>
                <td class="name left"><a href="symbol_downloader_py.html#t99"><data value='download_symbols_from_urls'>download_symbols_from_urls</data></a></td>
                <td>25</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="0 25">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="symbol_downloader_py.html#t151">symbol_downloader.py</a></td>
                <td class="name left"><a href="symbol_downloader_py.html#t151"><data value='cleanup_old_backups'>cleanup_old_backups</data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="symbol_downloader_py.html#t191">symbol_downloader.py</a></td>
                <td class="name left"><a href="symbol_downloader_py.html#t191"><data value='main'>main</data></a></td>
                <td>27</td>
                <td>27</td>
                <td>0</td>
                <td class="right" data-ratio="0 27">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="symbol_downloader_py.html">symbol_downloader.py</a></td>
                <td class="name left"><a href="symbol_downloader_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="technical_indicators_py.html#t10">technical_indicators.py</a></td>
                <td class="name left"><a href="technical_indicators_py.html#t10"><data value='init__'>MAEAnalyzer.__init__</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="technical_indicators_py.html#t17">technical_indicators.py</a></td>
                <td class="name left"><a href="technical_indicators_py.html#t17"><data value='get_source_series'>MAEAnalyzer.get_source_series</data></a></td>
                <td>14</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="14 14">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="technical_indicators_py.html#t39">technical_indicators.py</a></td>
                <td class="name left"><a href="technical_indicators_py.html#t39"><data value='calculate_mae'>MAEAnalyzer.calculate_mae</data></a></td>
                <td>13</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="13 13">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="technical_indicators_py.html#t68">technical_indicators.py</a></td>
                <td class="name left"><a href="technical_indicators_py.html#t68"><data value='is_price_passing_through_mae'>MAEAnalyzer.is_price_passing_through_mae</data></a></td>
                <td>18</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="14 18">78%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="technical_indicators_py.html">technical_indicators.py</a></td>
                <td class="name left"><a href="technical_indicators_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8___init___py.html">test\__init__.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_all_market_combinations_py.html#t24">test\test_all_market_combinations.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_all_market_combinations_py.html#t24"><data value='init__'>MarketTypeTester.__init__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_all_market_combinations_py.html#t41">test\test_all_market_combinations.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_all_market_combinations_py.html#t41"><data value='test_market_combination'>MarketTypeTester.test_market_combination</data></a></td>
                <td>26</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="0 26">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_all_market_combinations_py.html#t106">test\test_all_market_combinations.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_all_market_combinations_py.html#t106"><data value='run_all_tests'>MarketTypeTester.run_all_tests</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_all_market_combinations_py.html#t137">test\test_all_market_combinations.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_all_market_combinations_py.html#t137"><data value='generate_test_report'>MarketTypeTester.generate_test_report</data></a></td>
                <td>32</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="0 32">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_all_market_combinations_py.html#t181">test\test_all_market_combinations.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_all_market_combinations_py.html#t181"><data value='validate_functionality'>MarketTypeTester.validate_functionality</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_all_market_combinations_py.html#t205">test\test_all_market_combinations.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_all_market_combinations_py.html#t205"><data value='main'>main</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_all_market_combinations_py.html">test\test_all_market_combinations.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_all_market_combinations_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_api_integration_py.html#t29">test\test_api_integration.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_api_integration_py.html#t29"><data value='setUp'>TestAPIIntegration.setUp</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_api_integration_py.html#t94">test\test_api_integration.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_api_integration_py.html#t94"><data value='tearDown'>TestAPIIntegration.tearDown</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_api_integration_py.html#t98">test\test_api_integration.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_api_integration_py.html#t98"><data value='test_fyers_client_initialization'>TestAPIIntegration.test_fyers_client_initialization</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_api_integration_py.html#t107">test\test_api_integration.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_api_integration_py.html#t107"><data value='test_fyers_authentication_success'>TestAPIIntegration.test_fyers_authentication_success</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_api_integration_py.html#t121">test\test_api_integration.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_api_integration_py.html#t121"><data value='test_fyers_authentication_failure'>TestAPIIntegration.test_fyers_authentication_failure</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_api_integration_py.html#t134">test\test_api_integration.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_api_integration_py.html#t134"><data value='test_get_quotes_without_authentication'>TestAPIIntegration.test_get_quotes_without_authentication</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_api_integration_py.html#t145">test\test_api_integration.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_api_integration_py.html#t145"><data value='test_get_quotes_with_authentication'>TestAPIIntegration.test_get_quotes_with_authentication</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_api_integration_py.html#t185">test\test_api_integration.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_api_integration_py.html#t185"><data value='test_get_quotes_api_error'>TestAPIIntegration.test_get_quotes_api_error</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_api_integration_py.html#t207">test\test_api_integration.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_api_integration_py.html#t207"><data value='test_get_quotes_rate_limiting'>TestAPIIntegration.test_get_quotes_rate_limiting</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_api_integration_py.html#t244">test\test_api_integration.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_api_integration_py.html#t244"><data value='test_get_quotes_batch_processing'>TestAPIIntegration.test_get_quotes_batch_processing</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_api_integration_py.html#t251">test\test_api_integration.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_api_integration_py.html#t251"><data value='mock_quotes_response'>TestAPIIntegration.test_get_quotes_batch_processing.mock_quotes_response</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_api_integration_py.html#t283">test\test_api_integration.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_api_integration_py.html#t283"><data value='test_get_historical_data_success'>TestAPIIntegration.test_get_historical_data_success</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_api_integration_py.html#t314">test\test_api_integration.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_api_integration_py.html#t314"><data value='test_get_historical_data_error'>TestAPIIntegration.test_get_historical_data_error</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_api_integration_py.html#t335">test\test_api_integration.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_api_integration_py.html#t335"><data value='test_market_data_parsing_edge_cases'>TestAPIIntegration.test_market_data_parsing_edge_cases</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_api_integration_py.html#t355">test\test_api_integration.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_api_integration_py.html#t355"><data value='test_market_data_parsing_zero_values'>TestAPIIntegration.test_market_data_parsing_zero_values</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_api_integration_py.html#t383">test\test_api_integration.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_api_integration_py.html#t383"><data value='setUp'>TestAPIErrorHandling.setUp</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_api_integration_py.html#t415">test\test_api_integration.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_api_integration_py.html#t415"><data value='tearDown'>TestAPIErrorHandling.tearDown</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_api_integration_py.html#t420">test\test_api_integration.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_api_integration_py.html#t420"><data value='test_network_timeout_error'>TestAPIErrorHandling.test_network_timeout_error</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_api_integration_py.html#t439">test\test_api_integration.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_api_integration_py.html#t439"><data value='test_connection_error'>TestAPIErrorHandling.test_connection_error</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_api_integration_py.html#t458">test\test_api_integration.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_api_integration_py.html#t458"><data value='test_retry_logic_with_exponential_backoff'>TestAPIErrorHandling.test_retry_logic_with_exponential_backoff</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_api_integration_py.html#t466">test\test_api_integration.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_api_integration_py.html#t466"><data value='mock_quotes_side_effect'>TestAPIErrorHandling.test_retry_logic_with_exponential_backoff.mock_quotes_side_effect</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_api_integration_py.html#t504">test\test_api_integration.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_api_integration_py.html#t504"><data value='test_max_retries_exceeded'>TestAPIErrorHandling.test_max_retries_exceeded</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_api_integration_py.html#t524">test\test_api_integration.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_api_integration_py.html#t524"><data value='test_partial_batch_failure'>TestAPIErrorHandling.test_partial_batch_failure</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_api_integration_py.html#t532">test\test_api_integration.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_api_integration_py.html#t532"><data value='mock_quotes_side_effect'>TestAPIErrorHandling.test_partial_batch_failure.mock_quotes_side_effect</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_api_integration_py.html#t567">test\test_api_integration.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_api_integration_py.html#t567"><data value='test_malformed_api_response'>TestAPIErrorHandling.test_malformed_api_response</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_api_integration_py.html#t588">test\test_api_integration.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_api_integration_py.html#t588"><data value='test_empty_api_response'>TestAPIErrorHandling.test_empty_api_response</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_api_integration_py.html#t606">test\test_api_integration.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_api_integration_py.html#t606"><data value='test_authentication_expiry_during_operation'>TestAPIErrorHandling.test_authentication_expiry_during_operation</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_api_integration_py.html#t626">test\test_api_integration.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_api_integration_py.html#t626"><data value='test_memory_optimization_large_datasets'>TestAPIErrorHandling.test_memory_optimization_large_datasets</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_api_integration_py.html#t645">test\test_api_integration.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_api_integration_py.html#t645"><data value='setUp'>TestAPIIntegrationWithScanners.setUp</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_api_integration_py.html#t678">test\test_api_integration.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_api_integration_py.html#t678"><data value='tearDown'>TestAPIIntegrationWithScanners.tearDown</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_api_integration_py.html#t683">test\test_api_integration.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_api_integration_py.html#t683"><data value='test_scanner_api_integration_failure'>TestAPIIntegrationWithScanners.test_scanner_api_integration_failure</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_api_integration_py.html#t699">test\test_api_integration.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_api_integration_py.html#t699"><data value='test_scanner_api_partial_data_retrieval'>TestAPIIntegrationWithScanners.test_scanner_api_partial_data_retrieval</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_api_integration_py.html">test\test_api_integration.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_api_integration_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>69</td>
                <td>69</td>
                <td>0</td>
                <td class="right" data-ratio="0 69">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_comprehensive_py.html#t33">test\test_comprehensive.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_comprehensive_py.html#t33"><data value='init__'>ComprehensiveTestSuite.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_comprehensive_py.html#t37">test\test_comprehensive.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_comprehensive_py.html#t37"><data value='setup_test_environment'>ComprehensiveTestSuite.setup_test_environment</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_comprehensive_py.html#t48">test\test_comprehensive.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_comprehensive_py.html#t48"><data value='cleanup_test_environment'>ComprehensiveTestSuite.cleanup_test_environment</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_comprehensive_py.html#t54">test\test_comprehensive.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_comprehensive_py.html#t54"><data value='test_configuration_loading'>ComprehensiveTestSuite.test_configuration_loading</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_comprehensive_py.html#t78">test\test_comprehensive.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_comprehensive_py.html#t78"><data value='test_symbol_parsing'>ComprehensiveTestSuite.test_symbol_parsing</data></a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_comprehensive_py.html#t116">test\test_comprehensive.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_comprehensive_py.html#t116"><data value='test_options_filtering'>ComprehensiveTestSuite.test_options_filtering</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_comprehensive_py.html#t150">test\test_comprehensive.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_comprehensive_py.html#t150"><data value='test_market_scanners'>ComprehensiveTestSuite.test_market_scanners</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_comprehensive_py.html#t170">test\test_comprehensive.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_comprehensive_py.html#t170"><data value='test_unified_scanner_dry_run'>ComprehensiveTestSuite.test_unified_scanner_dry_run</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_comprehensive_py.html#t188">test\test_comprehensive.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_comprehensive_py.html#t188"><data value='run_all_tests'>ComprehensiveTestSuite.run_all_tests</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_comprehensive_py.html#t215">test\test_comprehensive.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_comprehensive_py.html#t215"><data value='print_test_summary'>ComprehensiveTestSuite.print_test_summary</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_comprehensive_py.html#t237">test\test_comprehensive.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_comprehensive_py.html#t237"><data value='main'>main</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_comprehensive_py.html">test\test_comprehensive.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_comprehensive_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>30</td>
                <td>30</td>
                <td>0</td>
                <td class="right" data-ratio="0 30">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_constant_py.html#t12">test\test_constant.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_constant_py.html#t12"><data value='test_market_types_constants'>TestConstants.test_market_types_constants</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_constant_py.html#t20">test\test_constant.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_constant_py.html#t20"><data value='test_market_types_list'>TestConstants.test_market_types_list</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_constant_py.html#t25">test\test_constant.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_constant_py.html#t25"><data value='test_constants_are_strings'>TestConstants.test_constants_are_strings</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_constant_py.html#t32">test\test_constant.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_constant_py.html#t32"><data value='test_market_types_list_is_list'>TestConstants.test_market_types_list_is_list</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_constant_py.html">test\test_constant.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_constant_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_end_to_end_integration_py.html#t30">test\test_end_to_end_integration.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_end_to_end_integration_py.html#t30"><data value='setUp'>TestEndToEndIntegration.setUp</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_end_to_end_integration_py.html#t93">test\test_end_to_end_integration.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_end_to_end_integration_py.html#t93"><data value='tearDown'>TestEndToEndIntegration.tearDown</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_end_to_end_integration_py.html#t98">test\test_end_to_end_integration.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_end_to_end_integration_py.html#t98"><data value='create_mock_market_data'>TestEndToEndIntegration.create_mock_market_data</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_end_to_end_integration_py.html#t116">test\test_end_to_end_integration.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_end_to_end_integration_py.html#t116"><data value='test_unified_scanner_initialization'>TestEndToEndIntegration.test_unified_scanner_initialization</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_end_to_end_integration_py.html#t123">test\test_end_to_end_integration.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_end_to_end_integration_py.html#t123"><data value='test_equity_scanner_end_to_end'>TestEndToEndIntegration.test_equity_scanner_end_to_end</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_end_to_end_integration_py.html#t149">test\test_end_to_end_integration.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_end_to_end_integration_py.html#t149"><data value='test_index_scanner_end_to_end'>TestEndToEndIntegration.test_index_scanner_end_to_end</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_end_to_end_integration_py.html#t174">test\test_end_to_end_integration.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_end_to_end_integration_py.html#t174"><data value='test_futures_scanner_end_to_end'>TestEndToEndIntegration.test_futures_scanner_end_to_end</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_end_to_end_integration_py.html#t199">test\test_end_to_end_integration.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_end_to_end_integration_py.html#t199"><data value='test_options_scanner_end_to_end'>TestEndToEndIntegration.test_options_scanner_end_to_end</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_end_to_end_integration_py.html#t229">test\test_end_to_end_integration.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_end_to_end_integration_py.html#t229"><data value='test_report_generator_initialization'>TestEndToEndIntegration.test_report_generator_initialization</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_end_to_end_integration_py.html#t235">test\test_end_to_end_integration.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_end_to_end_integration_py.html#t235"><data value='test_report_generator_file_creation'>TestEndToEndIntegration.test_report_generator_file_creation</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_end_to_end_integration_py.html#t271">test\test_end_to_end_integration.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_end_to_end_integration_py.html#t271"><data value='test_configuration_validation_end_to_end'>TestEndToEndIntegration.test_configuration_validation_end_to_end</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_end_to_end_integration_py.html#t291">test\test_end_to_end_integration.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_end_to_end_integration_py.html#t291"><data value='test_output_directory_creation'>TestEndToEndIntegration.test_output_directory_creation</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_end_to_end_integration_py.html#t306">test\test_end_to_end_integration.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_end_to_end_integration_py.html#t306"><data value='test_file_permissions_and_access'>TestEndToEndIntegration.test_file_permissions_and_access</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_end_to_end_integration_py.html#t337">test\test_end_to_end_integration.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_end_to_end_integration_py.html#t337"><data value='test_complete_workflow_with_all_market_types'>TestEndToEndIntegration.test_complete_workflow_with_all_market_types</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_end_to_end_integration_py.html">test\test_end_to_end_integration.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_end_to_end_integration_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>38</td>
                <td>38</td>
                <td>0</td>
                <td class="right" data-ratio="0 38">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_filter_combinations_mock_py.html#t37">test\test_filter_combinations_mock.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_filter_combinations_mock_py.html#t37"><data value='init__'>MockFilterCombinationTestSuite.__init__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_filter_combinations_mock_py.html#t43">test\test_filter_combinations_mock.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_filter_combinations_mock_py.html#t43"><data value='setup_test_environment'>MockFilterCombinationTestSuite.setup_test_environment</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_filter_combinations_mock_py.html#t57">test\test_filter_combinations_mock.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_filter_combinations_mock_py.html#t57"><data value='setup_mock_data'>MockFilterCombinationTestSuite.setup_mock_data</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_filter_combinations_mock_py.html#t170">test\test_filter_combinations_mock.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_filter_combinations_mock_py.html#t170"><data value='create_test_config'>MockFilterCombinationTestSuite.create_test_config</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_filter_combinations_mock_py.html#t232">test\test_filter_combinations_mock.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_filter_combinations_mock_py.html#t232"><data value='validate_config_combination'>MockFilterCombinationTestSuite.validate_config_combination</data></a></td>
                <td>34</td>
                <td>34</td>
                <td>0</td>
                <td class="right" data-ratio="0 34">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_filter_combinations_mock_py.html#t283">test\test_filter_combinations_mock.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_filter_combinations_mock_py.html#t283"><data value='test_all_market_types_basic'>MockFilterCombinationTestSuite.test_all_market_types_basic</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_filter_combinations_mock_py.html#t317">test\test_filter_combinations_mock.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_filter_combinations_mock_py.html#t317"><data value='test_pivot_point_only'>MockFilterCombinationTestSuite.test_pivot_point_only</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_filter_combinations_mock_py.html#t350">test\test_filter_combinations_mock.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_filter_combinations_mock_py.html#t350"><data value='test_mae_only'>MockFilterCombinationTestSuite.test_mae_only</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_filter_combinations_mock_py.html#t384">test\test_filter_combinations_mock.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_filter_combinations_mock_py.html#t384"><data value='test_ce_pe_pairing_only'>MockFilterCombinationTestSuite.test_ce_pe_pairing_only</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_filter_combinations_mock_py.html#t417">test\test_filter_combinations_mock.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_filter_combinations_mock_py.html#t417"><data value='test_ce_pe_pairing_and_pivot_point'>MockFilterCombinationTestSuite.test_ce_pe_pairing_and_pivot_point</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_filter_combinations_mock_py.html#t450">test\test_filter_combinations_mock.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_filter_combinations_mock_py.html#t450"><data value='test_ce_pe_pairing_and_mae_default'>MockFilterCombinationTestSuite.test_ce_pe_pairing_and_mae_default</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_filter_combinations_mock_py.html#t484">test\test_filter_combinations_mock.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_filter_combinations_mock_py.html#t484"><data value='test_ce_pe_pairing_and_mae_smoothed'>MockFilterCombinationTestSuite.test_ce_pe_pairing_and_mae_smoothed</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_filter_combinations_mock_py.html#t518">test\test_filter_combinations_mock.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_filter_combinations_mock_py.html#t518"><data value='test_all_filters_enabled'>MockFilterCombinationTestSuite.test_all_filters_enabled</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_filter_combinations_mock_py.html#t552">test\test_filter_combinations_mock.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_filter_combinations_mock_py.html#t552"><data value='test_market_type_specific_combinations'>MockFilterCombinationTestSuite.test_market_type_specific_combinations</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_filter_combinations_mock_py.html#t619">test\test_filter_combinations_mock.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_filter_combinations_mock_py.html#t619"><data value='test_volume_and_ltp_filter_combinations'>MockFilterCombinationTestSuite.test_volume_and_ltp_filter_combinations</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_filter_combinations_mock_py.html#t678">test\test_filter_combinations_mock.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_filter_combinations_mock_py.html#t678"><data value='cleanup_test_environment'>MockFilterCombinationTestSuite.cleanup_test_environment</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_filter_combinations_mock_py.html#t684">test\test_filter_combinations_mock.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_filter_combinations_mock_py.html#t684"><data value='run_all_tests'>MockFilterCombinationTestSuite.run_all_tests</data></a></td>
                <td>26</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="0 26">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_filter_combinations_mock_py.html#t725">test\test_filter_combinations_mock.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_filter_combinations_mock_py.html#t725"><data value='print_test_summary'>MockFilterCombinationTestSuite.print_test_summary</data></a></td>
                <td>41</td>
                <td>41</td>
                <td>0</td>
                <td class="right" data-ratio="0 41">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_filter_combinations_mock_py.html#t787">test\test_filter_combinations_mock.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_filter_combinations_mock_py.html#t787"><data value='main'>main</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_filter_combinations_mock_py.html">test\test_filter_combinations_mock.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_filter_combinations_mock_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>42</td>
                <td>42</td>
                <td>0</td>
                <td class="right" data-ratio="0 42">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_fixes_validation_py.html#t31">test\test_fixes_validation.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_fixes_validation_py.html#t31"><data value='test_market_data_validation'>test_market_data_validation</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_fixes_validation_py.html#t96">test\test_fixes_validation.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_fixes_validation_py.html#t96"><data value='test_pivot_point_calculation'>test_pivot_point_calculation</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_fixes_validation_py.html#t136">test\test_fixes_validation.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_fixes_validation_py.html#t136"><data value='test_caching_mechanism'>test_caching_mechanism</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_fixes_validation_py.html#t173">test\test_fixes_validation.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_fixes_validation_py.html#t173"><data value='main'>main</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_fixes_validation_py.html">test\test_fixes_validation.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_fixes_validation_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_mae_indicator_py.html#t29">test\test_mae_indicator.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_mae_indicator_py.html#t29"><data value='setUp'>TestMAEIndicator.setUp</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_mae_indicator_py.html#t89">test\test_mae_indicator.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_mae_indicator_py.html#t89"><data value='tearDown'>TestMAEIndicator.tearDown</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_mae_indicator_py.html#t93">test\test_mae_indicator.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_mae_indicator_py.html#t93"><data value='create_mock_ohlc_data'>TestMAEIndicator.create_mock_ohlc_data</data></a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_mae_indicator_py.html#t117">test\test_mae_indicator.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_mae_indicator_py.html#t117"><data value='test_mae_analyzer_initialization'>TestMAEIndicator.test_mae_analyzer_initialization</data></a></td>
                <td>12</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="12 12">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_mae_indicator_py.html#t141">test\test_mae_indicator.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_mae_indicator_py.html#t141"><data value='test_mae_source_series_extraction'>TestMAEIndicator.test_mae_source_series_extraction</data></a></td>
                <td>27</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="27 27">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_mae_indicator_py.html#t185">test\test_mae_indicator.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_mae_indicator_py.html#t185"><data value='test_mae_calculation'>TestMAEIndicator.test_mae_calculation</data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_mae_indicator_py.html#t205">test\test_mae_indicator.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_mae_indicator_py.html#t205"><data value='test_mae_calculation_with_offset'>TestMAEIndicator.test_mae_calculation_with_offset</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_mae_indicator_py.html#t218">test\test_mae_indicator.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_mae_indicator_py.html#t218"><data value='test_mae_price_passing_through'>TestMAEIndicator.test_mae_price_passing_through</data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_mae_indicator_py.html#t242">test\test_mae_indicator.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_mae_indicator_py.html#t242"><data value='test_mae_configuration_loading'>TestMAEIndicator.test_mae_configuration_loading</data></a></td>
                <td>16</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="16 16">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_mae_indicator_py.html#t268">test\test_mae_indicator.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_mae_indicator_py.html#t268"><data value='test_mae_mutual_exclusivity_with_pivot_point'>TestMAEIndicator.test_mae_mutual_exclusivity_with_pivot_point</data></a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_mae_indicator_py.html#t287">test\test_mae_indicator.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_mae_indicator_py.html#t287"><data value='test_mae_filter_integration_equity'>TestMAEIndicator.test_mae_filter_integration_equity</data></a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_mae_indicator_py.html#t336">test\test_mae_indicator.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_mae_indicator_py.html#t336"><data value='test_mae_filter_with_insufficient_data'>TestMAEIndicator.test_mae_filter_with_insufficient_data</data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_mae_indicator_py.html#t370">test\test_mae_indicator.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_mae_indicator_py.html#t370"><data value='test_mae_filter_disabled'>TestMAEIndicator.test_mae_filter_disabled</data></a></td>
                <td>12</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="12 12">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_mae_indicator_py.html#t408">test\test_mae_indicator.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_mae_indicator_py.html#t408"><data value='test_mae_smoothing_configurations'>TestMAEIndicator.test_mae_smoothing_configurations</data></a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_mae_indicator_py.html#t427">test\test_mae_indicator.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_mae_indicator_py.html#t427"><data value='test_mae_error_handling'>TestMAEIndicator.test_mae_error_handling</data></a></td>
                <td>12</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="10 12">83%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_mae_indicator_py.html#t450">test\test_mae_indicator.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_mae_indicator_py.html#t450"><data value='test_mae_filter_error_recovery'>TestMAEIndicator.test_mae_filter_error_recovery</data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_mae_indicator_py.html#t488">test\test_mae_indicator.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_mae_indicator_py.html#t488"><data value='setUp'>TestMAEIntegrationWithMarketScanners.setUp</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_mae_indicator_py.html#t547">test\test_mae_indicator.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_mae_indicator_py.html#t547"><data value='tearDown'>TestMAEIntegrationWithMarketScanners.tearDown</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_mae_indicator_py.html#t552">test\test_mae_indicator.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_mae_indicator_py.html#t552"><data value='test_mae_integration_with_index_scanner'>TestMAEIntegrationWithMarketScanners.test_mae_integration_with_index_scanner</data></a></td>
                <td>12</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="12 12">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_mae_indicator_py.html#t599">test\test_mae_indicator.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_mae_indicator_py.html#t599"><data value='test_mae_integration_with_futures_scanner'>TestMAEIntegrationWithMarketScanners.test_mae_integration_with_futures_scanner</data></a></td>
                <td>12</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="12 12">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_mae_indicator_py.html">test\test_mae_indicator.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_mae_indicator_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>44</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="43 44">98%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_main_py.html#t20">test\test_main.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_main_py.html#t20"><data value='setUp'>TestMainFunctionality.setUp</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_main_py.html#t26">test\test_main.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_main_py.html#t26"><data value='tearDown'>TestMainFunctionality.tearDown</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_main_py.html#t30">test\test_main.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_main_py.html#t30"><data value='test_print_banner'>TestMainFunctionality.test_print_banner</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_main_py.html#t46">test\test_main.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_main_py.html#t46"><data value='test_check_prerequisites_missing_config'>TestMainFunctionality.test_check_prerequisites_missing_config</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_main_py.html#t63">test\test_main.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_main_py.html#t63"><data value='test_check_prerequisites_with_config'>TestMainFunctionality.test_check_prerequisites_with_config</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_main_py.html#t84">test\test_main.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_main_py.html#t84"><data value='test_check_prerequisites_missing_packages'>TestMainFunctionality.test_check_prerequisites_missing_packages</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_main_py.html#t105">test\test_main.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_main_py.html#t105"><data value='test_check_prerequisites_csv_files_missing'>TestMainFunctionality.test_check_prerequisites_csv_files_missing</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_main_py.html#t133">test\test_main.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_main_py.html#t133"><data value='test_main_function_success'>TestMainFunctionality.test_main_function_success</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_main_py.html#t159">test\test_main.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_main_py.html#t159"><data value='test_main_function_scanner_failure'>TestMainFunctionality.test_main_function_scanner_failure</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_main_py.html#t178">test\test_main.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_main_py.html#t178"><data value='test_main_function_keyboard_interrupt'>TestMainFunctionality.test_main_function_keyboard_interrupt</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_main_py.html#t202">test\test_main.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_main_py.html#t202"><data value='test_main_function_exception'>TestMainFunctionality.test_main_function_exception</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_main_py.html#t225">test\test_main.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_main_py.html#t225"><data value='test_base_nifty50_symbols_constant'>TestMainFunctionality.test_base_nifty50_symbols_constant</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_main_py.html#t253">test\test_main.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_main_py.html#t253"><data value='test_main_entry_point_success'>TestMainFunctionality.test_main_entry_point_success</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_main_py.html#t281">test\test_main.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_main_py.html#t281"><data value='test_main_entry_point_prereq_failure'>TestMainFunctionality.test_main_entry_point_prereq_failure</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_main_py.html">test\test_main.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_main_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>42</td>
                <td>42</td>
                <td>0</td>
                <td class="right" data-ratio="0 42">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_main_functionality_py.html#t16">test\test_main_functionality.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_main_functionality_py.html#t16"><data value='create_test_config'>create_test_config</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_main_functionality_py.html#t70">test\test_main_functionality.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_main_functionality_py.html#t70"><data value='test_symbol_parsing'>test_symbol_parsing</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_main_functionality_py.html#t96">test\test_main_functionality.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_main_functionality_py.html#t96"><data value='test_market_scanners'>test_market_scanners</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_main_functionality_py.html#t117">test\test_main_functionality.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_main_functionality_py.html#t117"><data value='test_options_filtering'>test_options_filtering</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_main_functionality_py.html#t152">test\test_main_functionality.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_main_functionality_py.html#t152"><data value='test_unified_scanner_dry_run'>test_unified_scanner_dry_run</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_main_functionality_py.html#t179">test\test_main_functionality.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_main_functionality_py.html#t179"><data value='cleanup_test_files'>cleanup_test_files</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_main_functionality_py.html#t194">test\test_main_functionality.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_main_functionality_py.html#t194"><data value='main'>main</data></a></td>
                <td>24</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="0 24">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_main_functionality_py.html">test\test_main_functionality.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_main_functionality_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_main_integration_py.html#t24">test\test_main_integration.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_main_integration_py.html#t24"><data value='test_main_dry_run'>test_main_dry_run</data></a></td>
                <td>29</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="0 29">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_main_integration_py.html#t75">test\test_main_integration.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_main_integration_py.html#t75"><data value='test_market_data_validation'>test_market_data_validation</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_main_integration_py.html#t110">test\test_main_integration.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_main_integration_py.html#t110"><data value='test_pivot_point_integration'>test_pivot_point_integration</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_main_integration_py.html#t140">test\test_main_integration.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_main_integration_py.html#t140"><data value='main'>main</data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_main_integration_py.html">test\test_main_integration.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_main_integration_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_multi_market_scanner_py.html#t32">test\test_multi_market_scanner.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_multi_market_scanner_py.html#t32"><data value='init__'>MultiMarketScannerTest.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_multi_market_scanner_py.html#t37">test\test_multi_market_scanner.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_multi_market_scanner_py.html#t37"><data value='test_config_loader'>MultiMarketScannerTest.test_config_loader</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_multi_market_scanner_py.html#t68">test\test_multi_market_scanner.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_multi_market_scanner_py.html#t68"><data value='test_symbol_downloader'>MultiMarketScannerTest.test_symbol_downloader</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_multi_market_scanner_py.html#t97">test\test_multi_market_scanner.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_multi_market_scanner_py.html#t97"><data value='test_universal_symbol_parser'>MultiMarketScannerTest.test_universal_symbol_parser</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_multi_market_scanner_py.html#t128">test\test_multi_market_scanner.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_multi_market_scanner_py.html#t128"><data value='test_options_chain_filter'>MultiMarketScannerTest.test_options_chain_filter</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_multi_market_scanner_py.html#t168">test\test_multi_market_scanner.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_multi_market_scanner_py.html#t168"><data value='test_market_type_scanners'>MultiMarketScannerTest.test_market_type_scanners</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_multi_market_scanner_py.html#t194">test\test_multi_market_scanner.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_multi_market_scanner_py.html#t194"><data value='test_symbol_filtering'>MultiMarketScannerTest.test_symbol_filtering</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_multi_market_scanner_py.html#t224">test\test_multi_market_scanner.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_multi_market_scanner_py.html#t224"><data value='run_all_tests'>MultiMarketScannerTest.run_all_tests</data></a></td>
                <td>28</td>
                <td>28</td>
                <td>0</td>
                <td class="right" data-ratio="0 28">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_multi_market_scanner_py.html#t273">test\test_multi_market_scanner.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_multi_market_scanner_py.html#t273"><data value='main'>main</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_multi_market_scanner_py.html">test\test_multi_market_scanner.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_multi_market_scanner_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>27</td>
                <td>27</td>
                <td>0</td>
                <td class="right" data-ratio="0 27">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_nifty_options_comprehensive_py.html#t25">test\test_nifty_options_comprehensive.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_nifty_options_comprehensive_py.html#t25"><data value='setUp'>TestNiftyOptionsComprehensive.setUp</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_nifty_options_comprehensive_py.html#t75">test\test_nifty_options_comprehensive.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_nifty_options_comprehensive_py.html#t75"><data value='test_weekly_symbol_parsing'>TestNiftyOptionsComprehensive.test_weekly_symbol_parsing</data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_nifty_options_comprehensive_py.html#t112">test\test_nifty_options_comprehensive.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_nifty_options_comprehensive_py.html#t112"><data value='test_pivot_point_calculations'>TestNiftyOptionsComprehensive.test_pivot_point_calculations</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_nifty_options_comprehensive_py.html#t146">test\test_nifty_options_comprehensive.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_nifty_options_comprehensive_py.html#t146"><data value='test_market_data_to_filtered_symbol_conversion'>TestNiftyOptionsComprehensive.test_market_data_to_filtered_symbol_conversion</data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_nifty_options_comprehensive_py.html#t200">test\test_nifty_options_comprehensive.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_nifty_options_comprehensive_py.html#t200"><data value='calculate_min_positive_pivot'>TestNiftyOptionsComprehensive._calculate_min_positive_pivot</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_nifty_options_comprehensive_py.html#t232">test\test_nifty_options_comprehensive.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_nifty_options_comprehensive_py.html#t232"><data value='test_monthly_expiry_configuration'>TestNiftyOptionsComprehensive.test_monthly_expiry_configuration</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_nifty_options_comprehensive_py.html#t253">test\test_nifty_options_comprehensive.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_nifty_options_comprehensive_py.html#t253"><data value='test_all_symbols_configuration'>TestNiftyOptionsComprehensive.test_all_symbols_configuration</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_nifty_options_comprehensive_py.html#t272">test\test_nifty_options_comprehensive.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_nifty_options_comprehensive_py.html#t272"><data value='test_pivot_point_filtering_integration'>TestNiftyOptionsComprehensive.test_pivot_point_filtering_integration</data></a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_nifty_options_comprehensive_py.html#t328">test\test_nifty_options_comprehensive.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_nifty_options_comprehensive_py.html#t328"><data value='test_symbol_parsing_edge_cases'>TestNiftyOptionsComprehensive.test_symbol_parsing_edge_cases</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_nifty_options_comprehensive_py.html">test\test_nifty_options_comprehensive.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_nifty_options_comprehensive_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_option_utils_py.html#t13">test\test_option_utils.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_option_utils_py.html#t13"><data value='test_black_scholes_delta_call'>TestOptionUtils.test_black_scholes_delta_call</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_option_utils_py.html#t32">test\test_option_utils.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_option_utils_py.html#t32"><data value='test_black_scholes_delta_put'>TestOptionUtils.test_black_scholes_delta_put</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_option_utils_py.html#t51">test\test_option_utils.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_option_utils_py.html#t51"><data value='test_black_scholes_delta_itm_call'>TestOptionUtils.test_black_scholes_delta_itm_call</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_option_utils_py.html#t67">test\test_option_utils.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_option_utils_py.html#t67"><data value='test_black_scholes_delta_otm_call'>TestOptionUtils.test_black_scholes_delta_otm_call</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_option_utils_py.html#t83">test\test_option_utils.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_option_utils_py.html#t83"><data value='test_black_scholes_delta_edge_cases'>TestOptionUtils.test_black_scholes_delta_edge_cases</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_option_utils_py.html#t103">test\test_option_utils.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_option_utils_py.html#t103"><data value='test_is_weekly_expiry_valid'>TestOptionUtils.test_is_weekly_expiry_valid</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_option_utils_py.html#t113">test\test_option_utils.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_option_utils_py.html#t113"><data value='test_parse_option_symbol'>TestOptionUtils.test_parse_option_symbol</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_option_utils_py.html#t125">test\test_option_utils.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_option_utils_py.html#t125"><data value='test_calculate_option_greeks'>TestOptionUtils.test_calculate_option_greeks</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_option_utils_py.html#t143">test\test_option_utils.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_option_utils_py.html#t143"><data value='test_get_option_moneyness'>TestOptionUtils.test_get_option_moneyness</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_option_utils_py.html#t157">test\test_option_utils.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_option_utils_py.html#t157"><data value='test_calculate_implied_volatility'>TestOptionUtils.test_calculate_implied_volatility</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_option_utils_py.html#t177">test\test_option_utils.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_option_utils_py.html#t177"><data value='test_get_time_to_expiry'>TestOptionUtils.test_get_time_to_expiry</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_option_utils_py.html#t188">test\test_option_utils.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_option_utils_py.html#t188"><data value='test_format_option_symbol'>TestOptionUtils.test_format_option_symbol</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_option_utils_py.html">test\test_option_utils.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_option_utils_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_options_delta_values_py.html#t24">test\test_options_delta_values.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_options_delta_values_py.html#t24"><data value='mock_config'>TestOptionsDeltaValues.mock_config</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_options_delta_values_py.html#t37">test\test_options_delta_values.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_options_delta_values_py.html#t37"><data value='sample_option_chain'>TestOptionsDeltaValues.sample_option_chain</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_options_delta_values_py.html#t70">test\test_options_delta_values.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_options_delta_values_py.html#t70"><data value='sample_market_data'>TestOptionsDeltaValues.sample_market_data</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_options_delta_values_py.html#t124">test\test_options_delta_values.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_options_delta_values_py.html#t124"><data value='test_delta_based_filtering_creates_delta_map'>TestOptionsDeltaValues.test_delta_based_filtering_creates_delta_map</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_options_delta_values_py.html#t150">test\test_options_delta_values.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_options_delta_values_py.html#t150"><data value='test_delta_values_preserved_in_filtered_symbols'>TestOptionsDeltaValues.test_delta_values_preserved_in_filtered_symbols</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_options_delta_values_py.html#t183">test\test_options_delta_values.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_options_delta_values_py.html#t183"><data value='test_csv_generation_includes_delta_values'>TestOptionsDeltaValues.test_csv_generation_includes_delta_values</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_options_delta_values_py.html#t225">test\test_options_delta_values.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_options_delta_values_py.html#t225"><data value='test_missing_delta_columns_handling'>TestOptionsDeltaValues.test_missing_delta_columns_handling</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_options_delta_values_py.html#t246">test\test_options_delta_values.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_options_delta_values_py.html#t246"><data value='test_invalid_delta_values_handling'>TestOptionsDeltaValues.test_invalid_delta_values_handling</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_options_delta_values_py.html#t274">test\test_options_delta_values.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_options_delta_values_py.html#t274"><data value='test_delta_filtering_error_logging'>TestOptionsDeltaValues.test_delta_filtering_error_logging</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_options_delta_values_py.html">test\test_options_delta_values.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_options_delta_values_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>26</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="0 26">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_options_prefiltering_py.html#t22">test\test_options_prefiltering.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_options_prefiltering_py.html#t22"><data value='test_options_prefiltering'>test_options_prefiltering</data></a></td>
                <td>50</td>
                <td>50</td>
                <td>0</td>
                <td class="right" data-ratio="0 50">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_options_prefiltering_py.html">test\test_options_prefiltering.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_options_prefiltering_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_performance_optimizations_py.html#t22">test\test_performance_optimizations.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_performance_optimizations_py.html#t22"><data value='mock_config'>TestPerformanceOptimizations.mock_config</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_performance_optimizations_py.html#t35">test\test_performance_optimizations.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_performance_optimizations_py.html#t35"><data value='mock_fyers_config'>TestPerformanceOptimizations.mock_fyers_config</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_performance_optimizations_py.html#t41">test\test_performance_optimizations.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_performance_optimizations_py.html#t41"><data value='test_symbol_parsing_cache_performance'>TestPerformanceOptimizations.test_symbol_parsing_cache_performance</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_performance_optimizations_py.html#t84">test\test_performance_optimizations.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_performance_optimizations_py.html#t84"><data value='test_fyers_connect_caching'>TestPerformanceOptimizations.test_fyers_connect_caching</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_performance_optimizations_py.html#t108">test\test_performance_optimizations.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_performance_optimizations_py.html#t108"><data value='test_option_chain_caching'>TestPerformanceOptimizations.test_option_chain_caching</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_performance_optimizations_py.html#t126">test\test_performance_optimizations.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_performance_optimizations_py.html#t126"><data value='test_memory_optimization_large_dataset'>TestPerformanceOptimizations.test_memory_optimization_large_dataset</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_performance_optimizations_py.html#t163">test\test_performance_optimizations.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_performance_optimizations_py.html#t163"><data value='test_cache_cleanup_functionality'>TestPerformanceOptimizations.test_cache_cleanup_functionality</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_performance_optimizations_py.html#t185">test\test_performance_optimizations.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_performance_optimizations_py.html#t185"><data value='test_batch_processing_optimization'>TestPerformanceOptimizations.test_batch_processing_optimization</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_performance_optimizations_py.html#t207">test\test_performance_optimizations.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_performance_optimizations_py.html#t207"><data value='test_performance_regression_prevention'>TestPerformanceOptimizations.test_performance_regression_prevention</data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_performance_optimizations_py.html">test\test_performance_optimizations.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_performance_optimizations_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_pivot_calculation_types_py.html#t15">test\test_pivot_calculation_types.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_pivot_calculation_types_py.html#t15"><data value='test_pivot_calculation_types'>test_pivot_calculation_types</data></a></td>
                <td>41</td>
                <td>41</td>
                <td>0</td>
                <td class="right" data-ratio="0 41">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_pivot_calculation_types_py.html#t54">test\test_pivot_calculation_types.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_pivot_calculation_types_py.html#t54"><data value='init__'>test_pivot_calculation_types.MockMarketData.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_pivot_calculation_types_py.html">test\test_pivot_calculation_types.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_pivot_calculation_types_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_pivot_point_core_py.html#t18">test\test_pivot_point_core.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_pivot_point_core_py.html#t18"><data value='setUp'>TestPivotPointCore.setUp</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_pivot_point_core_py.html#t29">test\test_pivot_point_core.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_pivot_point_core_py.html#t29"><data value='test_calculate_pivot_points_basic'>TestPivotPointCore.test_calculate_pivot_points_basic</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_pivot_point_core_py.html#t60">test\test_pivot_point_core.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_pivot_point_core_py.html#t60"><data value='test_calculate_pivot_points_edge_cases'>TestPivotPointCore.test_calculate_pivot_points_edge_cases</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_pivot_point_core_py.html#t78">test\test_pivot_point_core.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_pivot_point_core_py.html#t78"><data value='test_calculate_daily_pivot_points'>TestPivotPointCore.test_calculate_daily_pivot_points</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_pivot_point_core_py.html#t93">test\test_pivot_point_core.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_pivot_point_core_py.html#t93"><data value='test_calculate_weekly_pivot_points'>TestPivotPointCore.test_calculate_weekly_pivot_points</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_pivot_point_core_py.html#t121">test\test_pivot_point_core.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_pivot_point_core_py.html#t121"><data value='test_calculate_monthly_pivot_points'>TestPivotPointCore.test_calculate_monthly_pivot_points</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_pivot_point_core_py.html#t149">test\test_pivot_point_core.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_pivot_point_core_py.html#t149"><data value='test_get_pivot_levels_for_symbol'>TestPivotPointCore.test_get_pivot_levels_for_symbol</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_pivot_point_core_py.html#t164">test\test_pivot_point_core.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_pivot_point_core_py.html#t164"><data value='test_filter_pivot_levels_by_ltp'>TestPivotPointCore.test_filter_pivot_levels_by_ltp</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_pivot_point_core_py.html#t187">test\test_pivot_point_core.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_pivot_point_core_py.html#t187"><data value='test_get_closest_pivot_levels'>TestPivotPointCore.test_get_closest_pivot_levels</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_pivot_point_core_py.html#t214">test\test_pivot_point_core.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_pivot_point_core_py.html#t214"><data value='test_validate_pivot_calculation_type'>TestPivotPointCore.test_validate_pivot_calculation_type</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_pivot_point_core_py.html#t226">test\test_pivot_point_core.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_pivot_point_core_py.html#t226"><data value='test_format_pivot_level_output'>TestPivotPointCore.test_format_pivot_level_output</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_pivot_point_core_py.html#t241">test\test_pivot_point_core.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_pivot_point_core_py.html#t241"><data value='test_calculate_pivot_level_distances'>TestPivotPointCore.test_calculate_pivot_level_distances</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_pivot_point_core_py.html#t259">test\test_pivot_point_core.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_pivot_point_core_py.html#t259"><data value='test_get_pivot_level_type_priority'>TestPivotPointCore.test_get_pivot_level_type_priority</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_pivot_point_core_py.html#t270">test\test_pivot_point_core.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_pivot_point_core_py.html#t270"><data value='test_merge_pivot_levels_from_multiple_timeframes'>TestPivotPointCore.test_merge_pivot_levels_from_multiple_timeframes</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_pivot_point_core_py.html#t293">test\test_pivot_point_core.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_pivot_point_core_py.html#t293"><data value='test_error_handling_invalid_ohlc_data'>TestPivotPointCore.test_error_handling_invalid_ohlc_data</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_pivot_point_core_py.html#t309">test\test_pivot_point_core.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_pivot_point_core_py.html#t309"><data value='test_performance_with_large_dataset'>TestPivotPointCore.test_performance_with_large_dataset</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_pivot_point_core_py.html">test\test_pivot_point_core.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_pivot_point_core_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>25</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="0 25">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_pivot_point_options_workflow_py.html#t47">test\test_pivot_point_options_workflow.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_pivot_point_options_workflow_py.html#t47"><data value='init__'>PivotPointOptionsWorkflowTest.__init__</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_pivot_point_options_workflow_py.html#t59">test\test_pivot_point_options_workflow.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_pivot_point_options_workflow_py.html#t59"><data value='setup_test_environment'>PivotPointOptionsWorkflowTest.setup_test_environment</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_pivot_point_options_workflow_py.html#t97">test\test_pivot_point_options_workflow.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_pivot_point_options_workflow_py.html#t97"><data value='parse_test_symbols'>PivotPointOptionsWorkflowTest.parse_test_symbols</data></a></td>
                <td>30</td>
                <td>30</td>
                <td>0</td>
                <td class="right" data-ratio="0 30">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_pivot_point_options_workflow_py.html#t151">test\test_pivot_point_options_workflow.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_pivot_point_options_workflow_py.html#t151"><data value='fetch_market_data'>PivotPointOptionsWorkflowTest.fetch_market_data</data></a></td>
                <td>35</td>
                <td>35</td>
                <td>0</td>
                <td class="right" data-ratio="0 35">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_pivot_point_options_workflow_py.html#t214">test\test_pivot_point_options_workflow.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_pivot_point_options_workflow_py.html#t214"><data value='create_mock_market_data'>PivotPointOptionsWorkflowTest._create_mock_market_data</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_pivot_point_options_workflow_py.html#t237">test\test_pivot_point_options_workflow.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_pivot_point_options_workflow_py.html#t237"><data value='calculate_pivot_points'>PivotPointOptionsWorkflowTest.calculate_pivot_points</data></a></td>
                <td>35</td>
                <td>35</td>
                <td>0</td>
                <td class="right" data-ratio="0 35">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_pivot_point_options_workflow_py.html#t293">test\test_pivot_point_options_workflow.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_pivot_point_options_workflow_py.html#t293"><data value='create_mock_pivot_data'>PivotPointOptionsWorkflowTest._create_mock_pivot_data</data></a></td>
                <td>25</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="0 25">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_pivot_point_options_workflow_py.html#t336">test\test_pivot_point_options_workflow.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_pivot_point_options_workflow_py.html#t336"><data value='generate_trading_signals'>PivotPointOptionsWorkflowTest.generate_trading_signals</data></a></td>
                <td>57</td>
                <td>57</td>
                <td>0</td>
                <td class="right" data-ratio="0 57">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_pivot_point_options_workflow_py.html#t414">test\test_pivot_point_options_workflow.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_pivot_point_options_workflow_py.html#t414"><data value='generate_comprehensive_report'>PivotPointOptionsWorkflowTest.generate_comprehensive_report</data></a></td>
                <td>75</td>
                <td>75</td>
                <td>0</td>
                <td class="right" data-ratio="0 75">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_pivot_point_options_workflow_py.html#t520">test\test_pivot_point_options_workflow.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_pivot_point_options_workflow_py.html#t520"><data value='print_console_summary'>PivotPointOptionsWorkflowTest.print_console_summary</data></a></td>
                <td>35</td>
                <td>35</td>
                <td>0</td>
                <td class="right" data-ratio="0 35">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_pivot_point_options_workflow_py.html#t571">test\test_pivot_point_options_workflow.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_pivot_point_options_workflow_py.html#t571"><data value='run_complete_workflow_test'>PivotPointOptionsWorkflowTest.run_complete_workflow_test</data></a></td>
                <td>34</td>
                <td>34</td>
                <td>0</td>
                <td class="right" data-ratio="0 34">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_pivot_point_options_workflow_py.html#t624">test\test_pivot_point_options_workflow.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_pivot_point_options_workflow_py.html#t624"><data value='main'>main</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_pivot_point_options_workflow_py.html">test\test_pivot_point_options_workflow.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_pivot_point_options_workflow_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>43</td>
                <td>43</td>
                <td>0</td>
                <td class="right" data-ratio="0 43">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_prefiltering_strike_levels_py.html#t28">test\test_prefiltering_strike_levels.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_prefiltering_strike_levels_py.html#t28"><data value='setUp'>TestPrefilteringStrikeLevels.setUp</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_prefiltering_strike_levels_py.html#t89">test\test_prefiltering_strike_levels.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_prefiltering_strike_levels_py.html#t89"><data value='tearDown'>TestPrefilteringStrikeLevels.tearDown</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_prefiltering_strike_levels_py.html#t93">test\test_prefiltering_strike_levels.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_prefiltering_strike_levels_py.html#t93"><data value='create_mock_options_symbols'>TestPrefilteringStrikeLevels.create_mock_options_symbols</data></a></td>
                <td>16</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="16 16">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_prefiltering_strike_levels_py.html#t138">test\test_prefiltering_strike_levels.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_prefiltering_strike_levels_py.html#t138"><data value='test_prefiltering_reduces_symbols_correctly'>TestPrefilteringStrikeLevels.test_prefiltering_reduces_symbols_correctly</data></a></td>
                <td>17</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="17 17">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_prefiltering_strike_levels_py.html#t182">test\test_prefiltering_strike_levels.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_prefiltering_strike_levels_py.html#t182"><data value='test_prefiltering_preserves_ce_pe_balance'>TestPrefilteringStrikeLevels.test_prefiltering_preserves_ce_pe_balance</data></a></td>
                <td>13</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="13 13">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_prefiltering_strike_levels_py.html#t217">test\test_prefiltering_strike_levels.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_prefiltering_strike_levels_py.html#t217"><data value='test_prefiltering_respects_strike_level_config'>TestPrefilteringStrikeLevels.test_prefiltering_respects_strike_level_config</data></a></td>
                <td>15</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="15 15">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_prefiltering_strike_levels_py.html#t262">test\test_prefiltering_strike_levels.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_prefiltering_strike_levels_py.html#t262"><data value='test_prefiltering_handles_no_spot_price'>TestPrefilteringStrikeLevels.test_prefiltering_handles_no_spot_price</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_prefiltering_strike_levels_py.html#t282">test\test_prefiltering_strike_levels.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_prefiltering_strike_levels_py.html#t282"><data value='test_prefiltering_with_empty_symbols'>TestPrefilteringStrikeLevels.test_prefiltering_with_empty_symbols</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_prefiltering_strike_levels_py.html#t293">test\test_prefiltering_strike_levels.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_prefiltering_strike_levels_py.html#t293"><data value='run_integration_test'>TestPrefilteringStrikeLevels.run_integration_test</data></a></td>
                <td>30</td>
                <td>30</td>
                <td>0</td>
                <td class="right" data-ratio="0 30">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_prefiltering_strike_levels_py.html#t352">test\test_prefiltering_strike_levels.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_prefiltering_strike_levels_py.html#t352"><data value='main'>main</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_prefiltering_strike_levels_py.html">test\test_prefiltering_strike_levels.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_prefiltering_strike_levels_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>27</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="25 27">93%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_report_generator_py.html#t22">test\test_report_generator.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_report_generator_py.html#t22"><data value='setUp'>TestReportGenerator.setUp</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_report_generator_py.html#t60">test\test_report_generator.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_report_generator_py.html#t60"><data value='tearDown'>TestReportGenerator.tearDown</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_report_generator_py.html#t65">test\test_report_generator.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_report_generator_py.html#t65"><data value='create_sample_filtered_symbols'>TestReportGenerator.create_sample_filtered_symbols</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_report_generator_py.html#t110">test\test_report_generator.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_report_generator_py.html#t110"><data value='test_report_generator_initialization'>TestReportGenerator.test_report_generator_initialization</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_report_generator_py.html#t116">test\test_report_generator.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_report_generator_py.html#t116"><data value='test_generate_reports_creates_files'>TestReportGenerator.test_generate_reports_creates_files</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_report_generator_py.html#t134">test\test_report_generator.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_report_generator_py.html#t134"><data value='test_generate_csv_report'>TestReportGenerator.test_generate_csv_report</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_report_generator_py.html#t157">test\test_report_generator.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_report_generator_py.html#t157"><data value='test_generate_text_report'>TestReportGenerator.test_generate_text_report</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_report_generator_py.html#t176">test\test_report_generator.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_report_generator_py.html#t176"><data value='test_generate_summary_report'>TestReportGenerator.test_generate_summary_report</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_report_generator_py.html#t197">test\test_report_generator.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_report_generator_py.html#t197"><data value='test_filter_symbols_by_market_type'>TestReportGenerator.test_filter_symbols_by_market_type</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_report_generator_py.html#t217">test\test_report_generator.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_report_generator_py.html#t217"><data value='test_sort_symbols_by_criteria'>TestReportGenerator.test_sort_symbols_by_criteria</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_report_generator_py.html#t233">test\test_report_generator.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_report_generator_py.html#t233"><data value='test_apply_top_n_filter'>TestReportGenerator.test_apply_top_n_filter</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_report_generator_py.html#t245">test\test_report_generator.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_report_generator_py.html#t245"><data value='test_format_symbol_for_output'>TestReportGenerator.test_format_symbol_for_output</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_report_generator_py.html#t268">test\test_report_generator.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_report_generator_py.html#t268"><data value='test_generate_market_type_specific_reports'>TestReportGenerator.test_generate_market_type_specific_reports</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_report_generator_py.html#t282">test\test_report_generator.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_report_generator_py.html#t282"><data value='test_error_handling_empty_symbols'>TestReportGenerator.test_error_handling_empty_symbols</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_report_generator_py.html#t295">test\test_report_generator.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_report_generator_py.html#t295"><data value='test_error_handling_invalid_output_directory'>TestReportGenerator.test_error_handling_invalid_output_directory</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_report_generator_py.html#t320">test\test_report_generator.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_report_generator_py.html#t320"><data value='test_timestamp_in_filenames'>TestReportGenerator.test_timestamp_in_filenames</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_report_generator_py.html#t334">test\test_report_generator.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_report_generator_py.html#t334"><data value='test_file_permissions'>TestReportGenerator.test_file_permissions</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_report_generator_py.html#t349">test\test_report_generator.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_report_generator_py.html#t349"><data value='test_large_dataset_handling'>TestReportGenerator.test_large_dataset_handling</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_report_generator_py.html">test\test_report_generator.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_report_generator_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>31</td>
                <td>31</td>
                <td>0</td>
                <td class="right" data-ratio="0 31">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_symbol_config_py.html#t13">test\test_symbol_config.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_symbol_config_py.html#t13"><data value='test_get_symbol_config_default'>TestSymbolConfig.test_get_symbol_config_default</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_symbol_config_py.html#t25">test\test_symbol_config.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_symbol_config_py.html#t25"><data value='test_get_symbol_config_with_custom_symbols'>TestSymbolConfig.test_get_symbol_config_with_custom_symbols</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_symbol_config_py.html#t37">test\test_symbol_config.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_symbol_config_py.html#t37"><data value='test_load_symbol_config_from_file'>TestSymbolConfig.test_load_symbol_config_from_file</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_symbol_config_py.html#t47">test\test_symbol_config.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_symbol_config_py.html#t47"><data value='test_validate_symbol_config'>TestSymbolConfig.test_validate_symbol_config</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_symbol_config_py.html#t60">test\test_symbol_config.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_symbol_config_py.html#t60"><data value='test_validate_symbol_config_invalid'>TestSymbolConfig.test_validate_symbol_config_invalid</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_symbol_config_py.html#t71">test\test_symbol_config.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_symbol_config_py.html#t71"><data value='test_get_symbols_by_market_type'>TestSymbolConfig.test_get_symbols_by_market_type</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_symbol_config_py.html#t91">test\test_symbol_config.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_symbol_config_py.html#t91"><data value='test_get_symbols_by_market_type_invalid'>TestSymbolConfig.test_get_symbols_by_market_type_invalid</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_symbol_config_py.html#t99">test\test_symbol_config.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_symbol_config_py.html#t99"><data value='test_merge_symbol_configs'>TestSymbolConfig.test_merge_symbol_configs</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_symbol_config_py.html#t120">test\test_symbol_config.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_symbol_config_py.html#t120"><data value='test_filter_symbols_by_pattern'>TestSymbolConfig.test_filter_symbols_by_pattern</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_symbol_config_py.html#t129">test\test_symbol_config.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_symbol_config_py.html#t129"><data value='test_get_default_symbols'>TestSymbolConfig.test_get_default_symbols</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_symbol_config_py.html">test\test_symbol_config.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_symbol_config_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_timeframe_py.html#t6">test\test_timeframe.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_timeframe_py.html#t6"><data value='test_fetch_intraday_data'>test_fetch_intraday_data</data></a></td>
                <td>27</td>
                <td>27</td>
                <td>0</td>
                <td class="right" data-ratio="0 27">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_timeframe_py.html">test\test_timeframe.py</a></td>
                <td class="name left"><a href="z_36f028580bb02cc8_test_timeframe_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="test_index_fixes_py.html#t15">test_index_fixes.py</a></td>
                <td class="name left"><a href="test_index_fixes_py.html#t15"><data value='test_nse_cm_symbol_loading'>test_nse_cm_symbol_loading</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="test_index_fixes_py.html#t46">test_index_fixes.py</a></td>
                <td class="name left"><a href="test_index_fixes_py.html#t46"><data value='test_index_symbol_resolution'>test_index_symbol_resolution</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="test_index_fixes_py.html#t74">test_index_fixes.py</a></td>
                <td class="name left"><a href="test_index_fixes_py.html#t74"><data value='test_index_pivot_point_calculation'>test_index_pivot_point_calculation</data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="test_index_fixes_py.html#t95">test_index_fixes.py</a></td>
                <td class="name left"><a href="test_index_fixes_py.html#t95"><data value='init__'>test_index_pivot_point_calculation.MockMarketData.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="test_index_fixes_py.html#t115">test_index_fixes.py</a></td>
                <td class="name left"><a href="test_index_fixes_py.html#t115"><data value='main'>main</data></a></td>
                <td>30</td>
                <td>30</td>
                <td>0</td>
                <td class="right" data-ratio="0 30">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="test_index_fixes_py.html">test_index_fixes.py</a></td>
                <td class="name left"><a href="test_index_fixes_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="unified_scanner_py.html#t24">unified_scanner.py</a></td>
                <td class="name left"><a href="unified_scanner_py.html#t24"><data value='init__'>UnifiedScanner.__init__</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="unified_scanner_py.html#t45">unified_scanner.py</a></td>
                <td class="name left"><a href="unified_scanner_py.html#t45"><data value='validate_prerequisites'>UnifiedScanner.validate_prerequisites</data></a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="unified_scanner_py.html#t84">unified_scanner.py</a></td>
                <td class="name left"><a href="unified_scanner_py.html#t84"><data value='download_symbol_files'>UnifiedScanner.download_symbol_files</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="unified_scanner_py.html#t103">unified_scanner.py</a></td>
                <td class="name left"><a href="unified_scanner_py.html#t103"><data value='scan_market_type'>UnifiedScanner.scan_market_type</data></a></td>
                <td>31</td>
                <td>31</td>
                <td>0</td>
                <td class="right" data-ratio="0 31">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="unified_scanner_py.html#t169">unified_scanner.py</a></td>
                <td class="name left"><a href="unified_scanner_py.html#t169"><data value='get_scan_summary'>UnifiedScanner.get_scan_summary</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="unified_scanner_py.html#t213">unified_scanner.py</a></td>
                <td class="name left"><a href="unified_scanner_py.html#t213"><data value='generate_combined_summary'>UnifiedScanner.generate_combined_summary</data></a></td>
                <td>58</td>
                <td>58</td>
                <td>0</td>
                <td class="right" data-ratio="0 58">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="unified_scanner_py.html#t298">unified_scanner.py</a></td>
                <td class="name left"><a href="unified_scanner_py.html#t298"><data value='run_unified_scan'>UnifiedScanner.run_unified_scan</data></a></td>
                <td>52</td>
                <td>52</td>
                <td>0</td>
                <td class="right" data-ratio="0 52">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="unified_scanner_py.html#t379">unified_scanner.py</a></td>
                <td class="name left"><a href="unified_scanner_py.html#t379"><data value='main'>main</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="unified_scanner_py.html">unified_scanner.py</a></td>
                <td class="name left"><a href="unified_scanner_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="universal_symbol_parser_py.html#t32">universal_symbol_parser.py</a></td>
                <td class="name left"><a href="universal_symbol_parser_py.html#t32"><data value='get_nse_symbol'>UniversalSymbol.get_nse_symbol</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="universal_symbol_parser_py.html#t36">universal_symbol_parser.py</a></td>
                <td class="name left"><a href="universal_symbol_parser_py.html#t36"><data value='get_base_symbol'>UniversalSymbol.get_base_symbol</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="universal_symbol_parser_py.html#t40">universal_symbol_parser.py</a></td>
                <td class="name left"><a href="universal_symbol_parser_py.html#t40"><data value='is_equity'>UniversalSymbol.is_equity</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="universal_symbol_parser_py.html#t44">universal_symbol_parser.py</a></td>
                <td class="name left"><a href="universal_symbol_parser_py.html#t44"><data value='is_index'>UniversalSymbol.is_index</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="universal_symbol_parser_py.html#t48">universal_symbol_parser.py</a></td>
                <td class="name left"><a href="universal_symbol_parser_py.html#t48"><data value='is_futures'>UniversalSymbol.is_futures</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="universal_symbol_parser_py.html#t52">universal_symbol_parser.py</a></td>
                <td class="name left"><a href="universal_symbol_parser_py.html#t52"><data value='is_options'>UniversalSymbol.is_options</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="universal_symbol_parser_py.html#t56">universal_symbol_parser.py</a></td>
                <td class="name left"><a href="universal_symbol_parser_py.html#t56"><data value='is_weekly_option'>UniversalSymbol.is_weekly_option</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="universal_symbol_parser_py.html#t62">universal_symbol_parser.py</a></td>
                <td class="name left"><a href="universal_symbol_parser_py.html#t62"><data value='is_monthly_option'>UniversalSymbol.is_monthly_option</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="universal_symbol_parser_py.html#t72">universal_symbol_parser.py</a></td>
                <td class="name left"><a href="universal_symbol_parser_py.html#t72"><data value='init__'>UniversalSymbolParser.__init__</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="universal_symbol_parser_py.html#t90">universal_symbol_parser.py</a></td>
                <td class="name left"><a href="universal_symbol_parser_py.html#t90"><data value='compile_patterns'>UniversalSymbolParser._compile_patterns</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="universal_symbol_parser_py.html#t122">universal_symbol_parser.py</a></td>
                <td class="name left"><a href="universal_symbol_parser_py.html#t122"><data value='parse_symbol'>UniversalSymbolParser.parse_symbol</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="universal_symbol_parser_py.html#t156">universal_symbol_parser.py</a></td>
                <td class="name left"><a href="universal_symbol_parser_py.html#t156"><data value='parse_by_market_type'>UniversalSymbolParser._parse_by_market_type</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="universal_symbol_parser_py.html#t168">universal_symbol_parser.py</a></td>
                <td class="name left"><a href="universal_symbol_parser_py.html#t168"><data value='parse_equity'>UniversalSymbolParser._parse_equity</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="universal_symbol_parser_py.html#t181">universal_symbol_parser.py</a></td>
                <td class="name left"><a href="universal_symbol_parser_py.html#t181"><data value='parse_index'>UniversalSymbolParser._parse_index</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="universal_symbol_parser_py.html#t194">universal_symbol_parser.py</a></td>
                <td class="name left"><a href="universal_symbol_parser_py.html#t194"><data value='parse_futures'>UniversalSymbolParser._parse_futures</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="universal_symbol_parser_py.html#t210">universal_symbol_parser.py</a></td>
                <td class="name left"><a href="universal_symbol_parser_py.html#t210"><data value='parse_options'>UniversalSymbolParser._parse_options</data></a></td>
                <td>38</td>
                <td>38</td>
                <td>0</td>
                <td class="right" data-ratio="0 38">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="universal_symbol_parser_py.html#t295">universal_symbol_parser.py</a></td>
                <td class="name left"><a href="universal_symbol_parser_py.html#t295"><data value='process_symbol_batch'>UniversalSymbolParser._process_symbol_batch</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="universal_symbol_parser_py.html#t317">universal_symbol_parser.py</a></td>
                <td class="name left"><a href="universal_symbol_parser_py.html#t317"><data value='parse_symbol_with_underlying'>UniversalSymbolParser.parse_symbol_with_underlying</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="universal_symbol_parser_py.html#t355">universal_symbol_parser.py</a></td>
                <td class="name left"><a href="universal_symbol_parser_py.html#t355"><data value='load_symbols_from_csv'>UniversalSymbolParser.load_symbols_from_csv</data></a></td>
                <td>46</td>
                <td>46</td>
                <td>0</td>
                <td class="right" data-ratio="0 46">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="universal_symbol_parser_py.html#t437">universal_symbol_parser.py</a></td>
                <td class="name left"><a href="universal_symbol_parser_py.html#t437"><data value='get_symbols_for_market_type'>UniversalSymbolParser.get_symbols_for_market_type</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="universal_symbol_parser_py.html#t473">universal_symbol_parser.py</a></td>
                <td class="name left"><a href="universal_symbol_parser_py.html#t473"><data value='filter_index_symbols_with_dynamic_mapping'>UniversalSymbolParser._filter_index_symbols_with_dynamic_mapping</data></a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="universal_symbol_parser_py.html#t531">universal_symbol_parser.py</a></td>
                <td class="name left"><a href="universal_symbol_parser_py.html#t531"><data value='get_options_symbols_with_early_filtering'>UniversalSymbolParser.get_options_symbols_with_early_filtering</data></a></td>
                <td>43</td>
                <td>43</td>
                <td>0</td>
                <td class="right" data-ratio="0 43">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="universal_symbol_parser_py.html">universal_symbol_parser.py</a></td>
                <td class="name left"><a href="universal_symbol_parser_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>43</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="43 43">100%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>8147</td>
                <td>7146</td>
                <td>0</td>
                <td class="right" data-ratio="1001 8147">12%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.0">coverage.py v7.8.0</a>,
            created at 2025-07-23 17:35 +0530
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
