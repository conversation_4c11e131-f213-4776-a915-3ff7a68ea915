<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_718ce007.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">12%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button current">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.0">coverage.py v7.8.0</a>,
            created at 2025-07-23 17:35 +0530
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="config_loader_py.html">config_loader.py</a></td>
                <td>210</td>
                <td>90</td>
                <td>0</td>
                <td class="right" data-ratio="120 210">57%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="constant_py.html">constant.py</a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="debug_nifty_weekly_py.html">debug_nifty_weekly.py</a></td>
                <td>143</td>
                <td>143</td>
                <td>0</td>
                <td class="right" data-ratio="0 143">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="fyers_client_py.html">fyers_client.py</a></td>
                <td>301</td>
                <td>260</td>
                <td>0</td>
                <td class="right" data-ratio="41 301">14%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="fyers_config_py.html">fyers_config.py</a></td>
                <td>164</td>
                <td>139</td>
                <td>0</td>
                <td class="right" data-ratio="25 164">15%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="fyers_connect_py.html">fyers_connect.py</a></td>
                <td>566</td>
                <td>529</td>
                <td>0</td>
                <td class="right" data-ratio="37 566">7%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="main_py.html">main.py</a></td>
                <td>64</td>
                <td>64</td>
                <td>0</td>
                <td class="right" data-ratio="0 64">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="market_type_scanner_py.html">market_type_scanner.py</a></td>
                <td>797</td>
                <td>672</td>
                <td>0</td>
                <td class="right" data-ratio="125 797">16%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="option_utils_py.html">option_utils.py</a></td>
                <td>86</td>
                <td>86</td>
                <td>0</td>
                <td class="right" data-ratio="0 86">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="options_chain_filter_py.html">options_chain_filter.py</a></td>
                <td>441</td>
                <td>283</td>
                <td>0</td>
                <td class="right" data-ratio="158 441">36%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="pivot_point_core_py.html">pivot_point_core.py</a></td>
                <td>260</td>
                <td>260</td>
                <td>0</td>
                <td class="right" data-ratio="0 260">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="pivot_point_integration_py.html">pivot_point_integration.py</a></td>
                <td>374</td>
                <td>339</td>
                <td>0</td>
                <td class="right" data-ratio="35 374">9%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="pivot_points_py.html">pivot_points.py</a></td>
                <td>86</td>
                <td>77</td>
                <td>0</td>
                <td class="right" data-ratio="9 86">10%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="report_generator_py.html">report_generator.py</a></td>
                <td>351</td>
                <td>351</td>
                <td>0</td>
                <td class="right" data-ratio="0 351">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="symbol_config_py.html">symbol_config.py</a></td>
                <td>29</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="0 29">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="symbol_downloader_py.html">symbol_downloader.py</a></td>
                <td>131</td>
                <td>131</td>
                <td>0</td>
                <td class="right" data-ratio="0 131">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="technical_indicators_py.html">technical_indicators.py</a></td>
                <td>59</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="55 59">93%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8___init___py.html">test\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_all_market_combinations_py.html">test\test_all_market_combinations.py</a></td>
                <td>127</td>
                <td>127</td>
                <td>0</td>
                <td class="right" data-ratio="0 127">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_api_integration_py.html">test\test_api_integration.py</a></td>
                <td>293</td>
                <td>293</td>
                <td>0</td>
                <td class="right" data-ratio="0 293">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_comprehensive_py.html">test\test_comprehensive.py</a></td>
                <td>152</td>
                <td>152</td>
                <td>0</td>
                <td class="right" data-ratio="0 152">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_constant_py.html">test\test_constant.py</a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_end_to_end_integration_py.html">test\test_end_to_end_integration.py</a></td>
                <td>155</td>
                <td>155</td>
                <td>0</td>
                <td class="right" data-ratio="0 155">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_filter_combinations_mock_py.html">test\test_filter_combinations_mock.py</a></td>
                <td>288</td>
                <td>288</td>
                <td>0</td>
                <td class="right" data-ratio="0 288">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_fixes_validation_py.html">test\test_fixes_validation.py</a></td>
                <td>92</td>
                <td>92</td>
                <td>0</td>
                <td class="right" data-ratio="0 92">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_mae_indicator_py.html">test\test_mae_indicator.py</a></td>
                <td>241</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="238 241">99%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_main_py.html">test\test_main.py</a></td>
                <td>165</td>
                <td>165</td>
                <td>0</td>
                <td class="right" data-ratio="0 165">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_main_functionality_py.html">test\test_main_functionality.py</a></td>
                <td>124</td>
                <td>124</td>
                <td>0</td>
                <td class="right" data-ratio="0 124">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_main_integration_py.html">test\test_main_integration.py</a></td>
                <td>96</td>
                <td>96</td>
                <td>0</td>
                <td class="right" data-ratio="0 96">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_multi_market_scanner_py.html">test\test_multi_market_scanner.py</a></td>
                <td>164</td>
                <td>164</td>
                <td>0</td>
                <td class="right" data-ratio="0 164">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_nifty_options_comprehensive_py.html">test\test_nifty_options_comprehensive.py</a></td>
                <td>177</td>
                <td>177</td>
                <td>0</td>
                <td class="right" data-ratio="0 177">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_option_utils_py.html">test\test_option_utils.py</a></td>
                <td>75</td>
                <td>75</td>
                <td>0</td>
                <td class="right" data-ratio="0 75">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_options_delta_values_py.html">test\test_options_delta_values.py</a></td>
                <td>95</td>
                <td>95</td>
                <td>0</td>
                <td class="right" data-ratio="0 95">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_options_prefiltering_py.html">test\test_options_prefiltering.py</a></td>
                <td>64</td>
                <td>64</td>
                <td>0</td>
                <td class="right" data-ratio="0 64">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_performance_optimizations_py.html">test\test_performance_optimizations.py</a></td>
                <td>122</td>
                <td>122</td>
                <td>0</td>
                <td class="right" data-ratio="0 122">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_pivot_calculation_types_py.html">test\test_pivot_calculation_types.py</a></td>
                <td>53</td>
                <td>53</td>
                <td>0</td>
                <td class="right" data-ratio="0 53">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_pivot_point_core_py.html">test\test_pivot_point_core.py</a></td>
                <td>159</td>
                <td>159</td>
                <td>0</td>
                <td class="right" data-ratio="0 159">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_pivot_point_options_workflow_py.html">test\test_pivot_point_options_workflow.py</a></td>
                <td>404</td>
                <td>404</td>
                <td>0</td>
                <td class="right" data-ratio="0 404">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_prefiltering_strike_levels_py.html">test\test_prefiltering_strike_levels.py</a></td>
                <td>149</td>
                <td>48</td>
                <td>0</td>
                <td class="right" data-ratio="101 149">68%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_report_generator_py.html">test\test_report_generator.py</a></td>
                <td>155</td>
                <td>155</td>
                <td>0</td>
                <td class="right" data-ratio="0 155">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_symbol_config_py.html">test\test_symbol_config.py</a></td>
                <td>65</td>
                <td>65</td>
                <td>0</td>
                <td class="right" data-ratio="0 65">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_36f028580bb02cc8_test_timeframe_py.html">test\test_timeframe.py</a></td>
                <td>34</td>
                <td>34</td>
                <td>0</td>
                <td class="right" data-ratio="0 34">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="test_index_fixes_py.html">test_index_fixes.py</a></td>
                <td>96</td>
                <td>96</td>
                <td>0</td>
                <td class="right" data-ratio="0 96">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="unified_scanner_py.html">unified_scanner.py</a></td>
                <td>222</td>
                <td>222</td>
                <td>0</td>
                <td class="right" data-ratio="0 222">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="universal_symbol_parser_py.html">universal_symbol_parser.py</a></td>
                <td>289</td>
                <td>232</td>
                <td>0</td>
                <td class="right" data-ratio="57 289">20%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td>8147</td>
                <td>7146</td>
                <td>0</td>
                <td class="right" data-ratio="1001 8147">12%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.0">coverage.py v7.8.0</a>,
            created at 2025-07-23 17:35 +0530
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href="universal_symbol_parser_py.html"></a>
        <a id="nextFileLink" class="nav" href="config_loader_py.html"></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
