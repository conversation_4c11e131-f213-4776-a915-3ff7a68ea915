"""
Tests for symbol_config.py module.
"""

import unittest
from unittest.mock import patch, mock_open
from symbol_config import *


class TestSymbolConfig(unittest.TestCase):
    """Test suite for symbol configuration."""

    def test_get_symbol_config_default(self):
        """Test getting default symbol configuration."""
        config = get_symbol_config()
        
        # Should return a dictionary
        self.assertIsInstance(config, dict)
        
        # Should have expected keys
        expected_keys = ['equity_symbols', 'index_symbols', 'futures_symbols', 'options_symbols']
        for key in expected_keys:
            self.assertIn(key, config)

    def test_get_symbol_config_with_custom_symbols(self):
        """Test getting symbol configuration with custom symbols."""
        custom_symbols = ['RELIANCE', 'TCS', 'INFY']
        config = get_symbol_config(custom_symbols)
        
        # Should return a dictionary
        self.assertIsInstance(config, dict)
        
        # Should contain custom symbols
        self.assertIn('equity_symbols', config)

    @patch('builtins.open', new_callable=mock_open, read_data='{"test": "data"}')
    def test_load_symbol_config_from_file(self, mock_file):
        """Test loading symbol configuration from file."""
        try:
            config = load_symbol_config_from_file('test_config.json')
            # Should attempt to open the file
            mock_file.assert_called_once_with('test_config.json', 'r')
        except Exception:
            # File operations might fail in test environment
            pass

    def test_validate_symbol_config(self):
        """Test symbol configuration validation."""
        # Valid config
        valid_config = {
            'equity_symbols': ['RELIANCE', 'TCS'],
            'index_symbols': ['NIFTY', 'BANKNIFTY'],
            'futures_symbols': ['NIFTY24JULFUT'],
            'options_symbols': ['NIFTY24JUL25000CE']
        }
        
        result = validate_symbol_config(valid_config)
        self.assertTrue(result)

    def test_validate_symbol_config_invalid(self):
        """Test symbol configuration validation with invalid config."""
        # Invalid config (missing required keys)
        invalid_config = {
            'equity_symbols': ['RELIANCE']
            # Missing other required keys
        }
        
        result = validate_symbol_config(invalid_config)
        self.assertFalse(result)

    def test_get_symbols_by_market_type(self):
        """Test getting symbols by market type."""
        config = get_symbol_config()
        
        # Test equity symbols
        equity_symbols = get_symbols_by_market_type(config, 'EQUITY')
        self.assertIsInstance(equity_symbols, list)
        
        # Test index symbols
        index_symbols = get_symbols_by_market_type(config, 'INDEX')
        self.assertIsInstance(index_symbols, list)
        
        # Test futures symbols
        futures_symbols = get_symbols_by_market_type(config, 'FUTURES')
        self.assertIsInstance(futures_symbols, list)
        
        # Test options symbols
        options_symbols = get_symbols_by_market_type(config, 'OPTIONS')
        self.assertIsInstance(options_symbols, list)

    def test_get_symbols_by_market_type_invalid(self):
        """Test getting symbols by invalid market type."""
        config = get_symbol_config()
        
        # Test with invalid market type
        result = get_symbols_by_market_type(config, 'INVALID')
        self.assertEqual(result, [])

    def test_merge_symbol_configs(self):
        """Test merging symbol configurations."""
        config1 = {
            'equity_symbols': ['RELIANCE', 'TCS'],
            'index_symbols': ['NIFTY']
        }
        
        config2 = {
            'equity_symbols': ['INFY'],
            'futures_symbols': ['NIFTY24JULFUT']
        }
        
        merged = merge_symbol_configs(config1, config2)
        
        # Should contain symbols from both configs
        self.assertIn('RELIANCE', merged['equity_symbols'])
        self.assertIn('TCS', merged['equity_symbols'])
        self.assertIn('INFY', merged['equity_symbols'])
        self.assertIn('NIFTY', merged['index_symbols'])
        self.assertIn('NIFTY24JULFUT', merged['futures_symbols'])

    def test_filter_symbols_by_pattern(self):
        """Test filtering symbols by pattern."""
        symbols = ['RELIANCE', 'TCS', 'INFY', 'HDFC', 'ICICI']
        
        # Filter symbols containing 'I'
        filtered = filter_symbols_by_pattern(symbols, 'I')
        expected = ['INFY', 'ICICI']
        self.assertEqual(set(filtered), set(expected))

    def test_get_default_symbols(self):
        """Test getting default symbols."""
        defaults = get_default_symbols()
        
        # Should return a dictionary with default symbols
        self.assertIsInstance(defaults, dict)
        self.assertIn('equity_symbols', defaults)
        self.assertIn('index_symbols', defaults)


if __name__ == '__main__':
    unittest.main()
