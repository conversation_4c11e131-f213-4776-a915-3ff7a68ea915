{"note": "This file is an internal implementation detail to speed up HTML report generation. Its format can change at any time. You might be looking for the JSON report: https://coverage.rtfd.io/cmd.html#cmd-json", "format": 5, "version": "7.8.0", "globals": "ded27873fa66423b66800ea3f33fe96b", "files": {"config_loader_py": {"hash": "75afdb8868ff34452d7caea60c17799c", "index": {"url": "config_loader_py.html", "file": "config_loader.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 210, "n_excluded": 0, "n_missing": 90, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "constant_py": {"hash": "e058b009faa67714005adea1b4e4ea1d", "index": {"url": "constant_py.html", "file": "constant.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 8, "n_excluded": 0, "n_missing": 8, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "debug_nifty_weekly_py": {"hash": "247b9e6d243ea46f1b7161a4fe330f4d", "index": {"url": "debug_nifty_weekly_py.html", "file": "debug_nifty_weekly.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 143, "n_excluded": 0, "n_missing": 143, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "fyers_client_py": {"hash": "e63edaa2f7488b2fca6227e0f7f803bb", "index": {"url": "fyers_client_py.html", "file": "fyers_client.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 301, "n_excluded": 0, "n_missing": 260, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "fyers_config_py": {"hash": "cd1ea1fecf288e36c5de5532f8662f65", "index": {"url": "fyers_config_py.html", "file": "fyers_config.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 164, "n_excluded": 0, "n_missing": 139, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "fyers_connect_py": {"hash": "94e8f9cf1765339f76d6d4b4b97cbacc", "index": {"url": "fyers_connect_py.html", "file": "fyers_connect.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 566, "n_excluded": 0, "n_missing": 529, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "main_py": {"hash": "075ce2b609fea93f4dfe96b3b2d2c6fb", "index": {"url": "main_py.html", "file": "main.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 64, "n_excluded": 0, "n_missing": 64, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "market_type_scanner_py": {"hash": "ff1bf65f62aef646b2ed5c625b0f6847", "index": {"url": "market_type_scanner_py.html", "file": "market_type_scanner.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 797, "n_excluded": 0, "n_missing": 672, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "option_utils_py": {"hash": "5461eed4755f8bc07dc387b70d5963d2", "index": {"url": "option_utils_py.html", "file": "option_utils.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 86, "n_excluded": 0, "n_missing": 86, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "options_chain_filter_py": {"hash": "9c0e0770811a098fa838b4551a177479", "index": {"url": "options_chain_filter_py.html", "file": "options_chain_filter.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 441, "n_excluded": 0, "n_missing": 283, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "pivot_point_core_py": {"hash": "a999536faad0fb61dca53db56113f582", "index": {"url": "pivot_point_core_py.html", "file": "pivot_point_core.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 260, "n_excluded": 0, "n_missing": 260, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "pivot_point_integration_py": {"hash": "6d89f07c99d63a3231160a2950527036", "index": {"url": "pivot_point_integration_py.html", "file": "pivot_point_integration.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 374, "n_excluded": 0, "n_missing": 339, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "pivot_points_py": {"hash": "72044ffc10bd41f802043a4580b660f6", "index": {"url": "pivot_points_py.html", "file": "pivot_points.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 86, "n_excluded": 0, "n_missing": 77, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "report_generator_py": {"hash": "0c010c3286332da28a5cb86270a2fb12", "index": {"url": "report_generator_py.html", "file": "report_generator.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 351, "n_excluded": 0, "n_missing": 351, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "symbol_config_py": {"hash": "8d799a482de97540fc1a99433aa4fa9d", "index": {"url": "symbol_config_py.html", "file": "symbol_config.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 29, "n_excluded": 0, "n_missing": 29, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "symbol_downloader_py": {"hash": "b3b1485a92e8df6915bb57cda80a07eb", "index": {"url": "symbol_downloader_py.html", "file": "symbol_downloader.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 131, "n_excluded": 0, "n_missing": 131, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "technical_indicators_py": {"hash": "6131e1295c0b6f305ac3838d40cb2402", "index": {"url": "technical_indicators_py.html", "file": "technical_indicators.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 59, "n_excluded": 0, "n_missing": 4, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_36f028580bb02cc8___init___py": {"hash": "db2b0f9c2f3d416da9dd4caa49f608ce", "index": {"url": "z_36f028580bb02cc8___init___py.html", "file": "test\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_36f028580bb02cc8_test_all_market_combinations_py": {"hash": "4436f25cd065077c3c4f6ad529ca9973", "index": {"url": "z_36f028580bb02cc8_test_all_market_combinations_py.html", "file": "test\\test_all_market_combinations.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 127, "n_excluded": 0, "n_missing": 127, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_36f028580bb02cc8_test_api_integration_py": {"hash": "e60386ed4cf56f54ed2837a5aeca31b7", "index": {"url": "z_36f028580bb02cc8_test_api_integration_py.html", "file": "test\\test_api_integration.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 293, "n_excluded": 0, "n_missing": 293, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_36f028580bb02cc8_test_comprehensive_py": {"hash": "bf8450ddfd61b062b70e551605508de7", "index": {"url": "z_36f028580bb02cc8_test_comprehensive_py.html", "file": "test\\test_comprehensive.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 152, "n_excluded": 0, "n_missing": 152, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_36f028580bb02cc8_test_constant_py": {"hash": "07670291c589170cf15a67b68bdcf5d8", "index": {"url": "z_36f028580bb02cc8_test_constant_py.html", "file": "test\\test_constant.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 21, "n_excluded": 0, "n_missing": 21, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_36f028580bb02cc8_test_end_to_end_integration_py": {"hash": "ce6662962e6dece7eced54762f599d82", "index": {"url": "z_36f028580bb02cc8_test_end_to_end_integration_py.html", "file": "test\\test_end_to_end_integration.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 155, "n_excluded": 0, "n_missing": 155, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_36f028580bb02cc8_test_filter_combinations_mock_py": {"hash": "0b5ce9d3380c1785a55b9a9a06b7c60b", "index": {"url": "z_36f028580bb02cc8_test_filter_combinations_mock_py.html", "file": "test\\test_filter_combinations_mock.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 288, "n_excluded": 0, "n_missing": 288, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_36f028580bb02cc8_test_fixes_validation_py": {"hash": "ed675dc0a21c278cad820046ec2caae0", "index": {"url": "z_36f028580bb02cc8_test_fixes_validation_py.html", "file": "test\\test_fixes_validation.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 92, "n_excluded": 0, "n_missing": 92, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_36f028580bb02cc8_test_mae_indicator_py": {"hash": "41dcfaaa2df6ae554e186726b46cf4f3", "index": {"url": "z_36f028580bb02cc8_test_mae_indicator_py.html", "file": "test\\test_mae_indicator.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 241, "n_excluded": 0, "n_missing": 3, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_36f028580bb02cc8_test_main_py": {"hash": "daf4e19332b60dede3d2ba7f4a0b774b", "index": {"url": "z_36f028580bb02cc8_test_main_py.html", "file": "test\\test_main.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 165, "n_excluded": 0, "n_missing": 165, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_36f028580bb02cc8_test_main_functionality_py": {"hash": "fba2ad8171db348d38123c3921886361", "index": {"url": "z_36f028580bb02cc8_test_main_functionality_py.html", "file": "test\\test_main_functionality.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 124, "n_excluded": 0, "n_missing": 124, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_36f028580bb02cc8_test_main_integration_py": {"hash": "0384db7d1e08733d77b6d9cdad2e6b3d", "index": {"url": "z_36f028580bb02cc8_test_main_integration_py.html", "file": "test\\test_main_integration.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 96, "n_excluded": 0, "n_missing": 96, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_36f028580bb02cc8_test_multi_market_scanner_py": {"hash": "d46cc71284fd69d07408bd28db07e91e", "index": {"url": "z_36f028580bb02cc8_test_multi_market_scanner_py.html", "file": "test\\test_multi_market_scanner.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 164, "n_excluded": 0, "n_missing": 164, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_36f028580bb02cc8_test_nifty_options_comprehensive_py": {"hash": "6d3c384f345e01b9e8b9ec5534fb452a", "index": {"url": "z_36f028580bb02cc8_test_nifty_options_comprehensive_py.html", "file": "test\\test_nifty_options_comprehensive.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 177, "n_excluded": 0, "n_missing": 177, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_36f028580bb02cc8_test_option_utils_py": {"hash": "3b10064497c9bb4d4d6e81657b92b21b", "index": {"url": "z_36f028580bb02cc8_test_option_utils_py.html", "file": "test\\test_option_utils.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 75, "n_excluded": 0, "n_missing": 75, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_36f028580bb02cc8_test_options_delta_values_py": {"hash": "c4d1b534da97e456e9f7d8b0d85a1158", "index": {"url": "z_36f028580bb02cc8_test_options_delta_values_py.html", "file": "test\\test_options_delta_values.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 95, "n_excluded": 0, "n_missing": 95, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_36f028580bb02cc8_test_options_prefiltering_py": {"hash": "d8473a8aa5c4c9c12e7e741a7fe432c4", "index": {"url": "z_36f028580bb02cc8_test_options_prefiltering_py.html", "file": "test\\test_options_prefiltering.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 64, "n_excluded": 0, "n_missing": 64, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_36f028580bb02cc8_test_performance_optimizations_py": {"hash": "b4364187f99bca9c2545238799bc23f3", "index": {"url": "z_36f028580bb02cc8_test_performance_optimizations_py.html", "file": "test\\test_performance_optimizations.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 122, "n_excluded": 0, "n_missing": 122, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_36f028580bb02cc8_test_pivot_calculation_types_py": {"hash": "500bb789d31f941e83c67290dbc1b58b", "index": {"url": "z_36f028580bb02cc8_test_pivot_calculation_types_py.html", "file": "test\\test_pivot_calculation_types.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 53, "n_excluded": 0, "n_missing": 53, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_36f028580bb02cc8_test_pivot_point_core_py": {"hash": "10bfca888592a9e8d967845bdfc53fd7", "index": {"url": "z_36f028580bb02cc8_test_pivot_point_core_py.html", "file": "test\\test_pivot_point_core.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 159, "n_excluded": 0, "n_missing": 159, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_36f028580bb02cc8_test_pivot_point_options_workflow_py": {"hash": "58d533fd8863dc5c3d56c1f82ecb8034", "index": {"url": "z_36f028580bb02cc8_test_pivot_point_options_workflow_py.html", "file": "test\\test_pivot_point_options_workflow.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 404, "n_excluded": 0, "n_missing": 404, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_36f028580bb02cc8_test_prefiltering_strike_levels_py": {"hash": "38e0242d1d7a13d70f5813720979e39c", "index": {"url": "z_36f028580bb02cc8_test_prefiltering_strike_levels_py.html", "file": "test\\test_prefiltering_strike_levels.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 149, "n_excluded": 0, "n_missing": 48, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_36f028580bb02cc8_test_report_generator_py": {"hash": "344cb94852a2c27030dfe46ed73a823b", "index": {"url": "z_36f028580bb02cc8_test_report_generator_py.html", "file": "test\\test_report_generator.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 155, "n_excluded": 0, "n_missing": 155, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_36f028580bb02cc8_test_symbol_config_py": {"hash": "f44c87d65a01db5f846988eaec0f5129", "index": {"url": "z_36f028580bb02cc8_test_symbol_config_py.html", "file": "test\\test_symbol_config.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 65, "n_excluded": 0, "n_missing": 65, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_36f028580bb02cc8_test_timeframe_py": {"hash": "8c1305ddeef8acfd76a68286cdd007e5", "index": {"url": "z_36f028580bb02cc8_test_timeframe_py.html", "file": "test\\test_timeframe.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 34, "n_excluded": 0, "n_missing": 34, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "test_index_fixes_py": {"hash": "142f85bd0f5e467b907660bf7da76f15", "index": {"url": "test_index_fixes_py.html", "file": "test_index_fixes.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 96, "n_excluded": 0, "n_missing": 96, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "unified_scanner_py": {"hash": "17ec2322ea3274be92e4e749bca1f516", "index": {"url": "unified_scanner_py.html", "file": "unified_scanner.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 222, "n_excluded": 0, "n_missing": 222, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "universal_symbol_parser_py": {"hash": "d6253264bb71b02b1c1e00749cc122cc", "index": {"url": "universal_symbol_parser_py.html", "file": "universal_symbol_parser.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 289, "n_excluded": 0, "n_missing": 232, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}}}