"""
Comprehensive tests for API integration, error handling, and recovery scenarios.
Tests cover real API integration, authentication, error handling, and recovery.
"""

import unittest
import yaml
import tempfile
import os
import time
from typing import List, Dict
from datetime import datetime
import logging
from unittest.mock import Mock, patch, MagicMock

from config_loader import ConfigLoader
from fyers_client import FyersClient, MarketData, OHLCData
from fyers_config import FyersConfig
from market_type_scanner import EquityScanner, IndexScanner, FuturesScanner, OptionsScanner

# Setup logging for test
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TestAPIIntegration(unittest.TestCase):
    """Test suite for API integration functionality."""

    def setUp(self):
        """Set up test fixtures."""
        # Create test configuration
        self.test_config = {
            'general': {
                'output_dir': 'test_reports',
                'fyers_api_url': ['https://public.fyers.in/sym_details/NSE_FO.csv']
            },
            'market_types': ['EQUITY', 'INDEX', 'FUTURES', 'OPTIONS'],
            'symbols': ['NIFTY', 'BANKNIFTY'],
            'volume_filter': {
                'min_volume': 1000,
                'max_volume': ********
            },
            'ltp_filter': {
                'min_ltp_price': 1.0,
                'max_ltp_price': 1000.0
            },
            'options_filter': {
                'strike_level': 10,
                'target_months': [],
                'expiry_type': 'MONTHLY',
                'min_delta': 0.30,
                'max_delta': 0.65
            },
            'timeframe': {
                'interval': 1,
                'days_to_fetch': 15
            },
            'mae_indicator': {
                'enabled': False,
                'length': 9,
                'source': 'close',
                'offset': 0,
                'smoothing_line': 'ema',
                'smoothing_length': 9,
                'smoothing_enabled': False
            },
            'pivot_point_indicator': {
                'enabled': False,
                'calculation_type': 'WEEKLY'
            },
            'report_filter': {
                'top_n_closest': 30
            },
            'ce_pe_pairing': {
                'enabled': False,
                'min_price_percent': 0.0,
                'max_price_percent': 3.0
            },
            'rate_limiting': {
                'min_delay_seconds': 0.1,
                'max_retries': 3,
                'retry_backoff': 2.0
            }
        }

        # Create temporary config file
        self.temp_config_file = tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False)
        yaml.dump(self.test_config, self.temp_config_file, default_flow_style=False)
        self.temp_config_file.close()

        # Load configuration
        self.config = ConfigLoader(self.temp_config_file.name)

    def tearDown(self):
        """Clean up test fixtures."""
        os.unlink(self.temp_config_file.name)

    def test_fyers_client_initialization(self):
        """Test Fyers client initialization."""
        client = FyersClient()
        self.assertIsNotNone(client)
        self.assertIsNotNone(client.fyers_config)
        self.assertIsNone(client.fyers_api)
        self.assertIsNone(client.access_token)

    @patch('fyers_config.FyersConfig.authenticate')
    def test_fyers_authentication_success(self, mock_authenticate):
        """Test successful Fyers authentication."""
        # Mock successful authentication
        mock_fyers_api = Mock()
        mock_authenticate.return_value = mock_fyers_api
        
        client = FyersClient()
        result = client.authenticate()
        
        self.assertTrue(result)
        self.assertIsNotNone(client.fyers_api)
        mock_authenticate.assert_called_once()

    @patch('fyers_config.FyersConfig.authenticate')
    def test_fyers_authentication_failure(self, mock_authenticate):
        """Test failed Fyers authentication."""
        # Mock failed authentication
        mock_authenticate.side_effect = Exception("Authentication failed")
        
        client = FyersClient()
        result = client.authenticate()
        
        self.assertFalse(result)
        self.assertIsNone(client.fyers_api)
        mock_authenticate.assert_called_once()

    @patch('fyers_config.FyersConfig.authenticate')
    def test_get_quotes_without_authentication(self, mock_authenticate):
        """Test get_quotes without authentication."""
        client = FyersClient()
        # Don't authenticate
        
        result = client.get_quotes(['NSE:RELIANCE-EQ'])
        
        self.assertEqual(result, {})

    @patch('fyers_client.FyersClient.authenticate')
    @patch('fyers_client.FyersClient.__init__')
    def test_get_quotes_with_authentication(self, mock_init, mock_authenticate):
        """Test get_quotes with successful authentication."""
        # Mock initialization
        mock_init.return_value = None

        # Create client and set up mocks
        client = FyersClient()
        client.fyers_config = Mock()

        # Mock successful authentication and API response
        mock_fyers_api = Mock()
        mock_authenticate.return_value = True
        client.fyers_api = mock_fyers_api
        client.access_token = "test_token"

        # Mock API response
        mock_response = {
            'code': 200,
            'd': {
                'NSE:RELIANCE-EQ': {
                    'v': {
                        'lp': 2500.0,
                        'volume': 1000000,
                        'open_price': 2490.0,
                        'high_price': 2520.0,
                        'low_price': 2480.0,
                        'prev_close_price': 2490.0
                    }
                }
            }
        }
        mock_fyers_api.quotes.return_value = mock_response

        result = client.get_quotes(['NSE:RELIANCE-EQ'])

        self.assertIn('NSE:RELIANCE-EQ', result)
        self.assertIsInstance(result['NSE:RELIANCE-EQ'], MarketData)
        self.assertEqual(result['NSE:RELIANCE-EQ'].ltp, 2500.0)

    @patch('fyers_config.FyersConfig.authenticate')
    def test_get_quotes_api_error(self, mock_authenticate):
        """Test get_quotes with API error response."""
        # Mock successful authentication but API error
        mock_fyers_api = Mock()
        mock_authenticate.return_value = mock_fyers_api
        
        # Mock API error response
        mock_response = {
            'code': 400,
            'message': 'Bad Request'
        }
        mock_fyers_api.quotes.return_value = mock_response
        
        client = FyersClient()
        client.authenticate()
        
        result = client.get_quotes(['NSE:RELIANCE-EQ'])
        
        # Should return empty dict on API error
        self.assertEqual(result, {})

    @patch('fyers_config.FyersConfig.authenticate')
    def test_get_quotes_rate_limiting(self, mock_authenticate):
        """Test get_quotes with rate limiting."""
        # Mock successful authentication
        mock_fyers_api = Mock()
        mock_authenticate.return_value = mock_fyers_api
        
        # Mock rate limit error followed by success
        mock_responses = [
            {'code': 429, 'message': 'Rate limit exceeded'},  # First call fails
            {  # Second call succeeds
                'code': 200,
                'd': {
                    'NSE:RELIANCE-EQ': {
                        'v': {
                            'lp': 2500.0,
                            'volume': 1000000,
                            'open_price': 2490.0,
                            'high_price': 2520.0,
                            'low_price': 2480.0,
                            'prev_close_price': 2490.0
                        }
                    }
                }
            }
        ]
        mock_fyers_api.quotes.side_effect = mock_responses
        
        client = FyersClient()
        client.authenticate()
        
        # Should retry and succeed
        result = client.get_quotes(['NSE:RELIANCE-EQ'])
        
        self.assertIn('NSE:RELIANCE-EQ', result)
        self.assertEqual(mock_fyers_api.quotes.call_count, 2)

    @patch('fyers_config.FyersConfig.authenticate')
    def test_get_quotes_batch_processing(self, mock_authenticate):
        """Test get_quotes with batch processing for large symbol lists."""
        # Mock successful authentication
        mock_fyers_api = Mock()
        mock_authenticate.return_value = mock_fyers_api
        
        # Mock API response for batch processing
        def mock_quotes_response(data):
            symbols = data['symbols'].split(',')
            response_data = {}
            for symbol in symbols:
                response_data[symbol] = {
                    'v': {
                        'lp': 100.0,
                        'volume': 1000,
                        'open_price': 99.0,
                        'high_price': 101.0,
                        'low_price': 98.0,
                        'prev_close_price': 99.0
                    }
                }
            return {'code': 200, 'd': response_data}
        
        mock_fyers_api.quotes.side_effect = mock_quotes_response
        mock_authenticate.return_value = mock_fyers_api
        
        client = FyersClient()
        client.authenticate()
        
        # Create large symbol list (more than batch size)
        symbols = [f'NSE:SYMBOL{i}-EQ' for i in range(100)]
        
        result = client.get_quotes(symbols)
        
        self.assertEqual(len(result), 100)
        # Should have made multiple API calls due to batching
        self.assertGreater(mock_fyers_api.quotes.call_count, 1)

    @patch('fyers_config.FyersConfig.authenticate')
    def test_get_historical_data_success(self, mock_authenticate):
        """Test successful historical data retrieval."""
        # Mock successful authentication
        mock_fyers_api = Mock()
        mock_authenticate.return_value = mock_fyers_api
        
        # Mock historical data response
        mock_response = {
            'code': 200,
            'candles': [
                [1640995200, 100.0, 105.0, 95.0, 102.0, 1000],  # timestamp, O, H, L, C, V
                [1641081600, 102.0, 107.0, 97.0, 104.0, 1100],
                [1641168000, 104.0, 109.0, 99.0, 106.0, 1200]
            ]
        }
        mock_fyers_api.history.return_value = mock_response
        
        client = FyersClient()
        client.authenticate()
        
        result = client.get_historical_data('NSE:RELIANCE-EQ', 1, 5)
        
        self.assertEqual(len(result), 3)
        self.assertIsInstance(result[0], OHLCData)
        self.assertEqual(result[0].open, 100.0)
        self.assertEqual(result[0].high, 105.0)
        self.assertEqual(result[0].low, 95.0)
        self.assertEqual(result[0].close, 102.0)
        self.assertEqual(result[0].volume, 1000)

    @patch('fyers_config.FyersConfig.authenticate')
    def test_get_historical_data_error(self, mock_authenticate):
        """Test historical data retrieval with API error."""
        # Mock successful authentication but API error
        mock_fyers_api = Mock()
        mock_authenticate.return_value = mock_fyers_api
        
        # Mock API error response
        mock_response = {
            'code': 400,
            'message': 'Invalid symbol'
        }
        mock_fyers_api.history.return_value = mock_response
        
        client = FyersClient()
        client.authenticate()
        
        result = client.get_historical_data('INVALID:SYMBOL', 1, 5)
        
        # Should return empty list on error
        self.assertEqual(result, [])

    def test_market_data_parsing_edge_cases(self):
        """Test market data parsing with edge cases."""
        client = FyersClient()
        
        # Test with missing fields
        incomplete_data = {
            'v': {
                'lp': 100.0,
                # Missing other fields
            }
        }
        
        result = client._parse_market_data('TEST:SYMBOL', incomplete_data)
        
        self.assertIsInstance(result, MarketData)
        self.assertEqual(result.symbol, 'TEST:SYMBOL')
        self.assertEqual(result.ltp, 100.0)
        # Other fields should have default values
        self.assertEqual(result.volume, 0)

    def test_market_data_parsing_zero_values(self):
        """Test market data parsing with zero OHLC values."""
        client = FyersClient()
        
        # Test with zero OHLC values
        zero_data = {
            'v': {
                'lp': 100.0,
                'volume': 1000,
                'open_price': 0.0,  # Zero values
                'high_price': 0.0,
                'low_price': 0.0,
                'prev_close_price': 0.0
            }
        }
        
        result = client._parse_market_data('TEST:SYMBOL', zero_data)
        
        self.assertIsInstance(result, MarketData)
        # Should use LTP as fallback for zero values
        self.assertEqual(result.open_price, 100.0)
        self.assertEqual(result.high_price, 100.0)
        self.assertEqual(result.low_price, 100.0)


class TestAPIErrorHandling(unittest.TestCase):
    """Test suite for API error handling and recovery scenarios."""

    def setUp(self):
        """Set up test fixtures."""
        self.test_config = {
            'general': {
                'output_dir': 'test_reports',
                'fyers_api_url': ['https://public.fyers.in/sym_details/NSE_FO.csv']
            },
            'market_types': ['EQUITY'],
            'symbols': ['RELIANCE'],
            'volume_filter': {
                'min_volume': 1000,
                'max_volume': ********
            },
            'ltp_filter': {
                'min_ltp_price': 1.0,
                'max_ltp_price': 1000.0
            },
            'rate_limiting': {
                'min_delay_seconds': 0.1,
                'max_retries': 3,
                'retry_backoff': 2.0
            }
        }

        # Create temporary config file
        self.temp_config_file = tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False)
        yaml.dump(self.test_config, self.temp_config_file, default_flow_style=False)
        self.temp_config_file.close()

        # Load configuration
        self.config = ConfigLoader(self.temp_config_file.name)

    def tearDown(self):
        """Clean up test fixtures."""
        os.unlink(self.temp_config_file.name)

    @patch('fyers_config.FyersConfig.authenticate')
    def test_network_timeout_error(self, mock_authenticate):
        """Test handling of network timeout errors."""
        # Mock successful authentication
        mock_fyers_api = Mock()
        mock_authenticate.return_value = mock_fyers_api

        # Mock network timeout
        import requests
        mock_fyers_api.quotes.side_effect = requests.exceptions.Timeout("Request timeout")

        client = FyersClient()
        client.authenticate()

        result = client.get_quotes(['NSE:RELIANCE-EQ'])

        # Should handle timeout gracefully
        self.assertEqual(result, {})

    @patch('fyers_config.FyersConfig.authenticate')
    def test_connection_error(self, mock_authenticate):
        """Test handling of connection errors."""
        # Mock successful authentication
        mock_fyers_api = Mock()
        mock_authenticate.return_value = mock_fyers_api

        # Mock connection error
        import requests
        mock_fyers_api.quotes.side_effect = requests.exceptions.ConnectionError("Connection failed")

        client = FyersClient()
        client.authenticate()

        result = client.get_quotes(['NSE:RELIANCE-EQ'])

        # Should handle connection error gracefully
        self.assertEqual(result, {})

    @patch('fyers_config.FyersConfig.authenticate')
    def test_retry_logic_with_exponential_backoff(self, mock_authenticate):
        """Test retry logic with exponential backoff."""
        # Mock successful authentication
        mock_fyers_api = Mock()
        mock_authenticate.return_value = mock_fyers_api

        # Mock multiple failures followed by success
        call_count = 0
        def mock_quotes_side_effect(*args, **kwargs):
            nonlocal call_count
            call_count += 1
            if call_count <= 2:  # First 2 calls fail
                return {'code': 500, 'message': 'Internal server error'}
            else:  # Third call succeeds
                return {
                    'code': 200,
                    'd': {
                        'NSE:RELIANCE-EQ': {
                            'v': {
                                'lp': 2500.0,
                                'volume': 1000000,
                                'open_price': 2490.0,
                                'high_price': 2520.0,
                                'low_price': 2480.0,
                                'prev_close_price': 2490.0
                            }
                        }
                    }
                }

        mock_fyers_api.quotes.side_effect = mock_quotes_side_effect

        client = FyersClient()
        client.authenticate()

        start_time = time.time()
        result = client.get_quotes(['NSE:RELIANCE-EQ'])
        end_time = time.time()

        # Should eventually succeed after retries
        self.assertIn('NSE:RELIANCE-EQ', result)
        self.assertEqual(call_count, 3)
        # Should have taken some time due to backoff
        self.assertGreater(end_time - start_time, 0.1)

    @patch('fyers_config.FyersConfig.authenticate')
    def test_max_retries_exceeded(self, mock_authenticate):
        """Test behavior when max retries are exceeded."""
        # Mock successful authentication
        mock_fyers_api = Mock()
        mock_authenticate.return_value = mock_fyers_api

        # Mock persistent failures
        mock_fyers_api.quotes.return_value = {'code': 500, 'message': 'Persistent error'}

        client = FyersClient()
        client.authenticate()

        result = client.get_quotes(['NSE:RELIANCE-EQ'])

        # Should give up after max retries
        self.assertEqual(result, {})
        # Should have made max_retries + 1 attempts
        self.assertEqual(mock_fyers_api.quotes.call_count, 4)  # 1 initial + 3 retries

    @patch('fyers_config.FyersConfig.authenticate')
    def test_partial_batch_failure(self, mock_authenticate):
        """Test handling of partial batch failures."""
        # Mock successful authentication
        mock_fyers_api = Mock()
        mock_authenticate.return_value = mock_fyers_api

        # Mock partial success in batch
        call_count = 0
        def mock_quotes_side_effect(*args, **kwargs):
            nonlocal call_count
            call_count += 1
            if call_count == 1:  # First batch fails
                return {'code': 500, 'message': 'Server error'}
            else:  # Second batch succeeds
                symbols = args[0]['symbols'].split(',')
                response_data = {}
                for symbol in symbols:
                    response_data[symbol] = {
                        'v': {
                            'lp': 100.0,
                            'volume': 1000,
                            'open_price': 99.0,
                            'high_price': 101.0,
                            'low_price': 98.0,
                            'prev_close_price': 99.0
                        }
                    }
                return {'code': 200, 'd': response_data}

        mock_fyers_api.quotes.side_effect = mock_quotes_side_effect

        client = FyersClient()
        client.authenticate()

        # Test with symbols that will be split into batches
        symbols = [f'NSE:SYMBOL{i}-EQ' for i in range(100)]
        result = client.get_quotes(symbols)

        # Should have some results despite partial failures
        self.assertGreater(len(result), 0)
        self.assertLess(len(result), len(symbols))  # Not all symbols due to partial failure

    @patch('fyers_config.FyersConfig.authenticate')
    def test_malformed_api_response(self, mock_authenticate):
        """Test handling of malformed API responses."""
        # Mock successful authentication
        mock_fyers_api = Mock()
        mock_authenticate.return_value = mock_fyers_api

        # Mock malformed response
        mock_fyers_api.quotes.return_value = {
            'code': 200,
            'd': "invalid_data_format"  # Should be dict, not string
        }

        client = FyersClient()
        client.authenticate()

        result = client.get_quotes(['NSE:RELIANCE-EQ'])

        # Should handle malformed response gracefully
        self.assertEqual(result, {})

    @patch('fyers_config.FyersConfig.authenticate')
    def test_empty_api_response(self, mock_authenticate):
        """Test handling of empty API responses."""
        # Mock successful authentication
        mock_fyers_api = Mock()
        mock_authenticate.return_value = mock_fyers_api

        # Mock empty response
        mock_fyers_api.quotes.return_value = None

        client = FyersClient()
        client.authenticate()

        result = client.get_quotes(['NSE:RELIANCE-EQ'])

        # Should handle empty response gracefully
        self.assertEqual(result, {})

    @patch('fyers_config.FyersConfig.authenticate')
    def test_authentication_expiry_during_operation(self, mock_authenticate):
        """Test handling of authentication expiry during operation."""
        # Mock successful authentication initially
        mock_fyers_api = Mock()
        mock_authenticate.return_value = mock_fyers_api

        # Mock authentication expiry error
        mock_fyers_api.quotes.return_value = {
            'code': 401,
            'message': 'Authentication expired'
        }

        client = FyersClient()
        client.authenticate()

        result = client.get_quotes(['NSE:RELIANCE-EQ'])

        # Should handle auth expiry gracefully
        self.assertEqual(result, {})

    def test_memory_optimization_large_datasets(self):
        """Test memory optimization for large datasets."""
        client = FyersClient()

        # Test with very large symbol list
        large_symbol_list = [f'NSE:SYMBOL{i}-EQ' for i in range(10000)]

        # Should not raise memory errors
        try:
            # This will fail due to no authentication, but should not cause memory issues
            result = client.get_quotes(large_symbol_list)
            self.assertEqual(result, {})  # Expected due to no auth
        except MemoryError:
            self.fail("Memory error occurred with large dataset")


class TestAPIIntegrationWithScanners(unittest.TestCase):
    """Test API integration with market scanners."""

    def setUp(self):
        """Set up test fixtures."""
        self.test_config = {
            'general': {
                'output_dir': 'test_reports',
                'fyers_api_url': ['https://public.fyers.in/sym_details/NSE_FO.csv']
            },
            'market_types': ['EQUITY'],
            'symbols': ['RELIANCE'],
            'volume_filter': {
                'min_volume': 1000,
                'max_volume': ********
            },
            'ltp_filter': {
                'min_ltp_price': 1.0,
                'max_ltp_price': 1000.0
            },
            'mae_indicator': {
                'enabled': False
            },
            'pivot_point_indicator': {
                'enabled': False
            }
        }

        # Create temporary config file
        self.temp_config_file = tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False)
        yaml.dump(self.test_config, self.temp_config_file, default_flow_style=False)
        self.temp_config_file.close()

        # Load configuration
        self.config = ConfigLoader(self.temp_config_file.name)

    def tearDown(self):
        """Clean up test fixtures."""
        os.unlink(self.temp_config_file.name)

    @patch('fyers_client.FyersClient')
    def test_scanner_api_integration_failure(self, mock_fyers_client):
        """Test scanner behavior when API integration fails."""
        # Mock Fyers client that fails authentication
        mock_client = Mock()
        mock_fyers_client.return_value = mock_client
        mock_client.authenticate.return_value = False

        # Create equity scanner
        equity_scanner = EquityScanner(self.config)

        # Should handle authentication failure gracefully
        result = equity_scanner.scan_symbols()

        self.assertEqual(result, [])

    @patch('fyers_client.FyersClient')
    def test_scanner_api_partial_data_retrieval(self, mock_fyers_client):
        """Test scanner behavior with partial data retrieval."""
        # Mock Fyers client with partial success
        mock_client = Mock()
        mock_fyers_client.return_value = mock_client
        mock_client.authenticate.return_value = True

        # Mock partial market data response
        mock_client.get_quotes.return_value = {
            'NSE:RELIANCE-EQ': MarketData(
                symbol='NSE:RELIANCE-EQ',
                ltp=2500.0,
                volume=1000000,
                open_price=2490.0,
                high_price=2520.0,
                low_price=2480.0,
                close_price=2500.0,
                prev_close=2490.0,
                change=10.0,
                change_percent=0.4
            )
            # Missing other symbols that were requested
        }

        # Create equity scanner
        equity_scanner = EquityScanner(self.config)
        equity_scanner.fyers_client = mock_client

        # Should handle partial data gracefully
        market_data = equity_scanner.fetch_market_data(['NSE:RELIANCE-EQ', 'NSE:TCS-EQ'])

        # Should return data for available symbols only
        self.assertEqual(len(market_data), 1)
        self.assertIn('NSE:RELIANCE-EQ', market_data)


if __name__ == '__main__':
    unittest.main()
