"""
Comprehensive tests for pivot_point_core.py functionality.
Tests cover pivot point calculations, different calculation types, and data handling.
"""

import unittest
import tempfile
import os
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, MagicMock

from pivot_point_core import *


class TestPivotPointCore(unittest.TestCase):
    """Test suite for pivot point core functionality."""

    def setUp(self):
        """Set up test fixtures."""
        # Create sample OHLC data for testing
        self.sample_ohlc_data = [
            {'date': '2024-01-01', 'open': 100.0, 'high': 110.0, 'low': 95.0, 'close': 105.0},
            {'date': '2024-01-02', 'open': 105.0, 'high': 115.0, 'low': 100.0, 'close': 110.0},
            {'date': '2024-01-03', 'open': 110.0, 'high': 120.0, 'low': 105.0, 'close': 115.0},
            {'date': '2024-01-04', 'open': 115.0, 'high': 125.0, 'low': 110.0, 'close': 120.0},
            {'date': '2024-01-05', 'open': 120.0, 'high': 130.0, 'low': 115.0, 'close': 125.0}
        ]

    def test_calculate_pivot_points_basic(self):
        """Test basic pivot point calculation."""
        # Test with simple OHLC values
        high = 110.0
        low = 95.0
        close = 105.0
        
        pivot_points = calculate_pivot_points(high, low, close)
        
        # Verify pivot point calculation: PP = (H + L + C) / 3
        expected_pp = (high + low + close) / 3
        self.assertAlmostEqual(pivot_points['PP'], expected_pp, places=2)
        
        # Verify support and resistance levels are calculated
        self.assertIn('R1', pivot_points)
        self.assertIn('R2', pivot_points)
        self.assertIn('R3', pivot_points)
        self.assertIn('S1', pivot_points)
        self.assertIn('S2', pivot_points)
        self.assertIn('S3', pivot_points)
        
        # Verify resistance levels are above pivot point
        self.assertGreater(pivot_points['R1'], pivot_points['PP'])
        self.assertGreater(pivot_points['R2'], pivot_points['R1'])
        self.assertGreater(pivot_points['R3'], pivot_points['R2'])
        
        # Verify support levels are below pivot point
        self.assertLess(pivot_points['S1'], pivot_points['PP'])
        self.assertLess(pivot_points['S2'], pivot_points['S1'])
        self.assertLess(pivot_points['S3'], pivot_points['S2'])

    def test_calculate_pivot_points_edge_cases(self):
        """Test pivot point calculation with edge cases."""
        # Test with equal OHLC values
        equal_value = 100.0
        pivot_points = calculate_pivot_points(equal_value, equal_value, equal_value)
        
        # All levels should be equal when OHLC are equal
        self.assertEqual(pivot_points['PP'], equal_value)
        
        # Test with very small values
        small_high = 0.01
        small_low = 0.005
        small_close = 0.008
        
        pivot_points_small = calculate_pivot_points(small_high, small_low, small_close)
        self.assertIsInstance(pivot_points_small['PP'], float)
        self.assertGreater(pivot_points_small['PP'], 0)

    def test_calculate_daily_pivot_points(self):
        """Test daily pivot point calculation."""
        daily_pivots = calculate_daily_pivot_points(self.sample_ohlc_data)
        
        # Should return a list of pivot points for each day
        self.assertIsInstance(daily_pivots, list)
        self.assertEqual(len(daily_pivots), len(self.sample_ohlc_data))
        
        # Each entry should have pivot point data
        for pivot_data in daily_pivots:
            self.assertIn('date', pivot_data)
            self.assertIn('PP', pivot_data)
            self.assertIn('R1', pivot_data)
            self.assertIn('S1', pivot_data)

    def test_calculate_weekly_pivot_points(self):
        """Test weekly pivot point calculation."""
        # Create data spanning multiple weeks
        weekly_data = []
        base_date = datetime(2024, 1, 1)  # Monday
        
        for i in range(14):  # 2 weeks of data
            date = base_date + timedelta(days=i)
            weekly_data.append({
                'date': date.strftime('%Y-%m-%d'),
                'open': 100.0 + i,
                'high': 110.0 + i,
                'low': 95.0 + i,
                'close': 105.0 + i
            })
        
        weekly_pivots = calculate_weekly_pivot_points(weekly_data)
        
        # Should return pivot points for each week
        self.assertIsInstance(weekly_pivots, list)
        self.assertGreater(len(weekly_pivots), 0)
        
        # Each entry should have weekly pivot data
        for pivot_data in weekly_pivots:
            self.assertIn('week_start', pivot_data)
            self.assertIn('week_end', pivot_data)
            self.assertIn('PP', pivot_data)

    def test_calculate_monthly_pivot_points(self):
        """Test monthly pivot point calculation."""
        # Create data spanning multiple months
        monthly_data = []
        
        for month in range(1, 4):  # 3 months
            for day in range(1, 11):  # 10 days per month
                date = datetime(2024, month, day)
                monthly_data.append({
                    'date': date.strftime('%Y-%m-%d'),
                    'open': 100.0 + day,
                    'high': 110.0 + day,
                    'low': 95.0 + day,
                    'close': 105.0 + day
                })
        
        monthly_pivots = calculate_monthly_pivot_points(monthly_data)
        
        # Should return pivot points for each month
        self.assertIsInstance(monthly_pivots, list)
        self.assertGreater(len(monthly_pivots), 0)
        
        # Each entry should have monthly pivot data
        for pivot_data in monthly_pivots:
            self.assertIn('month', pivot_data)
            self.assertIn('year', pivot_data)
            self.assertIn('PP', pivot_data)

    def test_get_pivot_levels_for_symbol(self):
        """Test getting pivot levels for a specific symbol."""
        symbol = 'NIFTY'
        calculation_type = 'DAILY'
        
        # Mock OHLC data retrieval
        with patch('pivot_point_core.get_ohlc_data_for_symbol') as mock_get_data:
            mock_get_data.return_value = self.sample_ohlc_data
            
            pivot_levels = get_pivot_levels_for_symbol(symbol, calculation_type)
            
            # Should return pivot levels
            self.assertIsInstance(pivot_levels, (list, dict))
            mock_get_data.assert_called_once_with(symbol)

    def test_filter_pivot_levels_by_ltp(self):
        """Test filtering pivot levels by LTP (Last Traded Price)."""
        # Create sample pivot levels
        pivot_levels = [
            {'level': 95.0, 'type': 'S2'},
            {'level': 100.0, 'type': 'S1'},
            {'level': 105.0, 'type': 'PP'},
            {'level': 110.0, 'type': 'R1'},
            {'level': 115.0, 'type': 'R2'}
        ]
        
        ltp = 107.0
        delta_range = 5.0
        
        filtered_levels = filter_pivot_levels_by_ltp(pivot_levels, ltp, delta_range)
        
        # Should return levels within delta range of LTP
        self.assertIsInstance(filtered_levels, list)
        
        for level in filtered_levels:
            distance = abs(level['level'] - ltp)
            self.assertLessEqual(distance, delta_range)

    def test_get_closest_pivot_levels(self):
        """Test getting closest pivot levels to LTP."""
        # Create sample pivot levels
        pivot_levels = [
            {'level': 95.0, 'type': 'S2'},
            {'level': 100.0, 'type': 'S1'},
            {'level': 105.0, 'type': 'PP'},
            {'level': 110.0, 'type': 'R1'},
            {'level': 115.0, 'type': 'R2'}
        ]
        
        ltp = 107.0
        top_n = 3
        
        closest_levels = get_closest_pivot_levels(pivot_levels, ltp, top_n)
        
        # Should return top N closest levels
        self.assertIsInstance(closest_levels, list)
        self.assertLessEqual(len(closest_levels), top_n)
        
        # Should be sorted by distance from LTP
        if len(closest_levels) > 1:
            for i in range(len(closest_levels) - 1):
                dist1 = abs(closest_levels[i]['level'] - ltp)
                dist2 = abs(closest_levels[i + 1]['level'] - ltp)
                self.assertLessEqual(dist1, dist2)

    def test_validate_pivot_calculation_type(self):
        """Test validation of pivot calculation types."""
        # Valid types
        valid_types = ['DAILY', 'WEEKLY', 'MONTHLY']
        for calc_type in valid_types:
            self.assertTrue(validate_pivot_calculation_type(calc_type))
        
        # Invalid types
        invalid_types = ['HOURLY', 'YEARLY', 'INVALID', '']
        for calc_type in invalid_types:
            self.assertFalse(validate_pivot_calculation_type(calc_type))

    def test_format_pivot_level_output(self):
        """Test formatting of pivot level output."""
        pivot_level = {
            'level': 105.75,
            'type': 'R1',
            'distance': 2.25
        }
        
        formatted = format_pivot_level_output(pivot_level)
        
        # Should return formatted string
        self.assertIsInstance(formatted, str)
        self.assertIn('R1', formatted)
        self.assertIn('105.75', formatted)

    def test_calculate_pivot_level_distances(self):
        """Test calculation of distances from LTP to pivot levels."""
        pivot_levels = [
            {'level': 100.0, 'type': 'S1'},
            {'level': 105.0, 'type': 'PP'},
            {'level': 110.0, 'type': 'R1'}
        ]
        
        ltp = 107.0
        
        levels_with_distances = calculate_pivot_level_distances(pivot_levels, ltp)
        
        # Should add distance field to each level
        for level in levels_with_distances:
            self.assertIn('distance', level)
            expected_distance = abs(level['level'] - ltp)
            self.assertAlmostEqual(level['distance'], expected_distance, places=2)

    def test_get_pivot_level_type_priority(self):
        """Test priority ordering of pivot level types."""
        # Test that pivot point has highest priority
        pp_priority = get_pivot_level_type_priority('PP')
        r1_priority = get_pivot_level_type_priority('R1')
        s1_priority = get_pivot_level_type_priority('S1')
        
        # PP should have higher priority than R1 and S1
        self.assertLess(pp_priority, r1_priority)
        self.assertLess(pp_priority, s1_priority)

    def test_merge_pivot_levels_from_multiple_timeframes(self):
        """Test merging pivot levels from multiple timeframes."""
        daily_levels = [
            {'level': 100.0, 'type': 'PP', 'timeframe': 'DAILY'},
            {'level': 105.0, 'type': 'R1', 'timeframe': 'DAILY'}
        ]
        
        weekly_levels = [
            {'level': 98.0, 'type': 'PP', 'timeframe': 'WEEKLY'},
            {'level': 108.0, 'type': 'R1', 'timeframe': 'WEEKLY'}
        ]
        
        merged_levels = merge_pivot_levels_from_multiple_timeframes([daily_levels, weekly_levels])
        
        # Should combine levels from all timeframes
        self.assertIsInstance(merged_levels, list)
        self.assertEqual(len(merged_levels), 4)
        
        # Should contain levels from both timeframes
        timeframes = [level['timeframe'] for level in merged_levels]
        self.assertIn('DAILY', timeframes)
        self.assertIn('WEEKLY', timeframes)

    def test_error_handling_invalid_ohlc_data(self):
        """Test error handling with invalid OHLC data."""
        # Test with empty data
        empty_result = calculate_daily_pivot_points([])
        self.assertEqual(empty_result, [])
        
        # Test with malformed data
        malformed_data = [{'invalid': 'data'}]
        try:
            result = calculate_daily_pivot_points(malformed_data)
            # Should handle gracefully
            self.assertIsInstance(result, list)
        except Exception:
            # Exception is acceptable for malformed data
            pass

    def test_performance_with_large_dataset(self):
        """Test performance with large dataset."""
        # Create large dataset
        large_dataset = []
        for i in range(1000):
            large_dataset.append({
                'date': f'2024-01-{(i % 30) + 1:02d}',
                'open': 100.0 + i * 0.1,
                'high': 110.0 + i * 0.1,
                'low': 95.0 + i * 0.1,
                'close': 105.0 + i * 0.1
            })
        
        # Should handle large dataset without issues
        try:
            result = calculate_daily_pivot_points(large_dataset)
            self.assertIsInstance(result, list)
            self.assertEqual(len(result), len(large_dataset))
        except Exception as e:
            self.fail(f"Failed to handle large dataset: {e}")


if __name__ == '__main__':
    unittest.main()
