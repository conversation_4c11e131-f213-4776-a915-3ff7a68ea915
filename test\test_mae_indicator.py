"""
Comprehensive tests for MAE (Moving Average Exponential) indicator functionality.
Tests cover calculation, configuration, integration, and filtering.
"""

import unittest
import yaml
import tempfile
import os
import pandas as pd
from typing import List, Dict
from datetime import datetime
import logging
from unittest.mock import Mock, patch, MagicMock

from config_loader import Config<PERSON>oader
from technical_indicators import MAEAnalyzer
from market_type_scanner import EquityScanner, IndexScanner, FuturesScanner, OptionsScanner
from fyers_client import MarketData, OHLCData

# Setup logging for test
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TestMAEIndicator(unittest.TestCase):
    """Test suite for MAE indicator functionality."""

    def setUp(self):
        """Set up test fixtures."""
        # Create test configuration
        self.test_config = {
            'general': {
                'output_dir': 'test_reports',
                'fyers_api_url': ['https://public.fyers.in/sym_details/NSE_FO.csv']
            },
            'market_types': ['EQUITY', 'INDEX', 'FUTURES', 'OPTIONS'],
            'symbols': ['NIFTY', 'BANKNIFTY'],
            'volume_filter': {
                'min_volume': 1000,
                'max_volume': ********
            },
            'ltp_filter': {
                'min_ltp_price': 1.0,
                'max_ltp_price': 1000.0
            },
            'options_filter': {
                'strike_level': 10,
                'target_months': [],
                'expiry_type': 'MONTHLY',
                'min_delta': 0.30,
                'max_delta': 0.65
            },
            'timeframe': {
                'interval': 1,
                'days_to_fetch': 15
            },
            'mae_indicator': {
                'enabled': True,
                'length': 9,
                'source': 'close',
                'offset': 0,
                'smoothing_line': 'ema',
                'smoothing_length': 9,
                'smoothing_enabled': False
            },
            'pivot_point_indicator': {
                'enabled': False,
                'calculation_type': 'WEEKLY'
            },
            'report_filter': {
                'top_n_closest': 30
            },
            'ce_pe_pairing': {
                'enabled': False,
                'min_price_percent': 0.0,
                'max_price_percent': 3.0
            }
        }

        # Create temporary config file
        self.temp_config_file = tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False)
        yaml.dump(self.test_config, self.temp_config_file, default_flow_style=False)
        self.temp_config_file.close()

        # Load configuration
        self.config = ConfigLoader(self.temp_config_file.name)

    def tearDown(self):
        """Clean up test fixtures."""
        os.unlink(self.temp_config_file.name)

    def create_mock_ohlc_data(self, num_candles: int = 20) -> List[OHLCData]:
        """Create mock OHLC data for testing."""
        ohlc_data = []
        base_price = 100.0
        
        for i in range(num_candles):
            # Create some price variation
            price_variation = (i % 5) * 0.5 - 1.0  # -1.0 to 1.5
            open_price = base_price + price_variation
            high_price = open_price + abs(price_variation) + 0.5
            low_price = open_price - abs(price_variation) - 0.5
            close_price = open_price + (price_variation * 0.5)
            
            ohlc_data.append(OHLCData(
                timestamp=datetime.now().timestamp() + i * 3600,  # 1 hour intervals
                open=open_price,
                high=high_price,
                low=low_price,
                close=close_price,
                volume=1000 + i * 100
            ))
        
        return ohlc_data

    def test_mae_analyzer_initialization(self):
        """Test MAE analyzer initialization with different configurations."""
        # Test default initialization
        mae_analyzer = MAEAnalyzer()
        self.assertEqual(mae_analyzer.length, 9)
        self.assertEqual(mae_analyzer.source, 'close')
        self.assertEqual(mae_analyzer.offset, 0)
        self.assertEqual(mae_analyzer.smoothing_line, 'ema')
        self.assertEqual(mae_analyzer.smoothing_length, 9)

        # Test custom initialization
        mae_analyzer_custom = MAEAnalyzer(
            length=14,
            source='hl2',
            offset=1,
            smoothing_line='sma',
            smoothing_length=5
        )
        self.assertEqual(mae_analyzer_custom.length, 14)
        self.assertEqual(mae_analyzer_custom.source, 'hl2')
        self.assertEqual(mae_analyzer_custom.offset, 1)
        self.assertEqual(mae_analyzer_custom.smoothing_line, 'sma')
        self.assertEqual(mae_analyzer_custom.smoothing_length, 5)

    def test_mae_source_series_extraction(self):
        """Test extraction of different price series from OHLC data."""
        mae_analyzer = MAEAnalyzer()
        ohlc_data = self.create_mock_ohlc_data(10)

        # Test close source (default)
        mae_analyzer.source = 'close'
        close_series = mae_analyzer.get_source_series(ohlc_data)
        self.assertEqual(len(close_series), 10)
        self.assertEqual(close_series.iloc[0], ohlc_data[0].close)

        # Test open source
        mae_analyzer.source = 'open'
        open_series = mae_analyzer.get_source_series(ohlc_data)
        self.assertEqual(open_series.iloc[0], ohlc_data[0].open)

        # Test high source
        mae_analyzer.source = 'high'
        high_series = mae_analyzer.get_source_series(ohlc_data)
        self.assertEqual(high_series.iloc[0], ohlc_data[0].high)

        # Test low source
        mae_analyzer.source = 'low'
        low_series = mae_analyzer.get_source_series(ohlc_data)
        self.assertEqual(low_series.iloc[0], ohlc_data[0].low)

        # Test hl2 source
        mae_analyzer.source = 'hl2'
        hl2_series = mae_analyzer.get_source_series(ohlc_data)
        expected_hl2 = (ohlc_data[0].high + ohlc_data[0].low) / 2
        self.assertEqual(hl2_series.iloc[0], expected_hl2)

        # Test hlc3 source
        mae_analyzer.source = 'hlc3'
        hlc3_series = mae_analyzer.get_source_series(ohlc_data)
        expected_hlc3 = (ohlc_data[0].high + ohlc_data[0].low + ohlc_data[0].close) / 3
        self.assertEqual(hlc3_series.iloc[0], expected_hlc3)

        # Test ohlc4 source
        mae_analyzer.source = 'ohlc4'
        ohlc4_series = mae_analyzer.get_source_series(ohlc_data)
        expected_ohlc4 = (ohlc_data[0].open + ohlc_data[0].high + ohlc_data[0].low + ohlc_data[0].close) / 4
        self.assertEqual(ohlc4_series.iloc[0], expected_ohlc4)

    def test_mae_calculation(self):
        """Test MAE calculation with different configurations."""
        mae_analyzer = MAEAnalyzer(length=5)
        ohlc_data = self.create_mock_ohlc_data(20)

        # Test MAE calculation
        mae_default, mae_smoothed = mae_analyzer.calculate_mae(ohlc_data)
        
        # Check that we get pandas Series
        self.assertIsInstance(mae_default, pd.Series)
        self.assertIsInstance(mae_smoothed, pd.Series)
        
        # Check that series have correct length
        self.assertEqual(len(mae_default), 20)
        self.assertEqual(len(mae_smoothed), 20)
        
        # Check that MAE values are calculated (not all NaN)
        self.assertFalse(mae_default.isna().all())
        self.assertFalse(mae_smoothed.isna().all())

    def test_mae_calculation_with_offset(self):
        """Test MAE calculation with offset."""
        mae_analyzer = MAEAnalyzer(length=5, offset=2)
        ohlc_data = self.create_mock_ohlc_data(20)

        mae_default, mae_smoothed = mae_analyzer.calculate_mae(ohlc_data)
        
        # With offset=2, first 2 values should be NaN due to shift
        self.assertTrue(pd.isna(mae_default.iloc[0]))
        self.assertTrue(pd.isna(mae_default.iloc[1]))
        self.assertTrue(pd.isna(mae_smoothed.iloc[0]))
        self.assertTrue(pd.isna(mae_smoothed.iloc[1]))

    def test_mae_price_passing_through(self):
        """Test MAE price passing through logic."""
        mae_analyzer = MAEAnalyzer(length=5)
        
        # Create OHLC data where MAE should be within high-low range
        ohlc_data = []
        for i in range(10):
            ohlc_data.append(OHLCData(
                timestamp=datetime.now().timestamp() + i * 3600,
                open=100.0,
                high=105.0,  # MAE should fall within this range
                low=95.0,
                close=100.0,
                volume=1000
            ))
        
        # Test with smoothed MAE
        is_passing_smoothed = mae_analyzer.is_price_passing_through_mae(ohlc_data, use_smoothed=True)
        self.assertIn(is_passing_smoothed, [True, False])

        # Test with default MAE
        is_passing_default = mae_analyzer.is_price_passing_through_mae(ohlc_data, use_smoothed=False)
        self.assertIn(is_passing_default, [True, False])

    def test_mae_configuration_loading(self):
        """Test MAE configuration loading from config file."""
        # Test MAE enabled
        self.assertTrue(self.config.mae_enabled)
        self.assertEqual(self.config.mae_length, 9)
        self.assertEqual(self.config.mae_source, 'close')
        self.assertEqual(self.config.mae_offset, 0)
        self.assertEqual(self.config.mae_smoothing_line, 'ema')
        self.assertEqual(self.config.mae_smoothing_length, 9)
        self.assertFalse(self.config.mae_smoothing_enabled)

        # Test MAE disabled configuration
        disabled_config = self.test_config.copy()
        disabled_config['mae_indicator']['enabled'] = False
        
        temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False)
        yaml.dump(disabled_config, temp_file, default_flow_style=False)
        temp_file.close()
        
        try:
            config_disabled = ConfigLoader(temp_file.name)
            self.assertFalse(config_disabled.mae_enabled)
        finally:
            os.unlink(temp_file.name)


    def test_mae_mutual_exclusivity_with_pivot_point(self):
        """Test that MAE and Pivot Point indicators are mutually exclusive."""
        # Test configuration with both enabled should fail validation
        invalid_config = self.test_config.copy()
        invalid_config['mae_indicator']['enabled'] = True
        invalid_config['pivot_point_indicator']['enabled'] = True

        temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False)
        yaml.dump(invalid_config, temp_file, default_flow_style=False)
        temp_file.close()

        try:
            config_invalid = ConfigLoader(temp_file.name)
            # Should fail validation
            self.assertFalse(config_invalid.validate_config())
        finally:
            os.unlink(temp_file.name)

    @patch('fyers_client.FyersClient')
    def test_mae_filter_integration_equity(self, mock_fyers_client):
        """Test MAE filter integration with equity scanner."""
        # Mock Fyers client
        mock_client = Mock()
        mock_fyers_client.return_value = mock_client
        mock_client.authenticate.return_value = True
        mock_client.get_historical_data.return_value = self.create_mock_ohlc_data(15)

        # Create equity scanner with MAE enabled
        equity_scanner = EquityScanner(self.config)
        equity_scanner.fyers_client = mock_client

        # Create mock market data
        mock_market_data = {
            'NSE:RELIANCE-EQ': MarketData(
                symbol='NSE:RELIANCE-EQ',
                ltp=2500.0,
                volume=1000000,
                open_price=2490.0,
                high_price=2520.0,
                low_price=2480.0,
                close_price=2500.0,
                prev_close=2490.0,
                change=10.0,
                change_percent=0.4
            ),
            'NSE:TCS-EQ': MarketData(
                symbol='NSE:TCS-EQ',
                ltp=3500.0,
                volume=500000,
                open_price=3520.0,
                high_price=3530.0,
                low_price=3490.0,
                close_price=3500.0,
                prev_close=3520.0,
                change=-20.0,
                change_percent=-0.57
            )
        }

        # Apply MAE filter
        filtered_data = equity_scanner.apply_mae_filter(mock_market_data)

        # Verify that MAE filter was applied
        self.assertIsInstance(filtered_data, dict)
        # Should have called get_historical_data for each symbol
        self.assertEqual(mock_client.get_historical_data.call_count, 2)

    @patch('fyers_client.FyersClient')
    def test_mae_filter_with_insufficient_data(self, mock_fyers_client):
        """Test MAE filter behavior with insufficient OHLC data."""
        # Mock Fyers client with insufficient data
        mock_client = Mock()
        mock_fyers_client.return_value = mock_client
        mock_client.authenticate.return_value = True
        mock_client.get_historical_data.return_value = self.create_mock_ohlc_data(5)  # Less than mae_length

        # Create equity scanner
        equity_scanner = EquityScanner(self.config)
        equity_scanner.fyers_client = mock_client

        # Create mock market data
        mock_market_data = {
            'NSE:RELIANCE-EQ': MarketData(
                symbol='NSE:RELIANCE-EQ',
                ltp=2500.0,
                volume=1000000,
                open_price=2490.0,
                high_price=2520.0,
                low_price=2480.0,
                close_price=2500.0,
                prev_close=2490.0,
                change=10.0,
                change_percent=0.4
            )
        }

        # Apply MAE filter
        filtered_data = equity_scanner.apply_mae_filter(mock_market_data)

        # Should skip symbols with insufficient data
        self.assertIsInstance(filtered_data, dict)

    def test_mae_filter_disabled(self):
        """Test MAE filter when disabled in configuration."""
        # Create config with MAE disabled
        disabled_config = self.test_config.copy()
        disabled_config['mae_indicator']['enabled'] = False

        temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False)
        yaml.dump(disabled_config, temp_file, default_flow_style=False)
        temp_file.close()

        try:
            config_disabled = ConfigLoader(temp_file.name)
            equity_scanner = EquityScanner(config_disabled)

            # Create mock market data
            mock_market_data = {
                'NSE:RELIANCE-EQ': MarketData(
                    symbol='NSE:RELIANCE-EQ',
                    ltp=2500.0,
                    volume=1000000,
                    open_price=2490.0,
                    high_price=2520.0,
                    low_price=2480.0,
                    close_price=2500.0,
                    prev_close=2490.0,
                    change=10.0,
                    change_percent=0.4
                )
            }

            # Apply MAE filter (should return original data unchanged)
            filtered_data = equity_scanner.apply_mae_filter(mock_market_data)

            # Should return original data when MAE is disabled
            self.assertEqual(filtered_data, mock_market_data)
        finally:
            os.unlink(temp_file.name)

    def test_mae_smoothing_configurations(self):
        """Test different MAE smoothing configurations."""
        ohlc_data = self.create_mock_ohlc_data(20)

        # Test SMA smoothing
        mae_sma = MAEAnalyzer(length=9, smoothing_line='sma', smoothing_length=5)
        mae_default_sma, mae_smoothed_sma = mae_sma.calculate_mae(ohlc_data)
        self.assertIsInstance(mae_smoothed_sma, pd.Series)

        # Test WMA smoothing
        mae_wma = MAEAnalyzer(length=9, smoothing_line='wma', smoothing_length=5)
        mae_default_wma, mae_smoothed_wma = mae_wma.calculate_mae(ohlc_data)
        self.assertIsInstance(mae_smoothed_wma, pd.Series)

        # Test EMA smoothing (default)
        mae_ema = MAEAnalyzer(length=9, smoothing_line='ema', smoothing_length=5)
        mae_default_ema, mae_smoothed_ema = mae_ema.calculate_mae(ohlc_data)
        self.assertIsInstance(mae_smoothed_ema, pd.Series)

    def test_mae_error_handling(self):
        """Test MAE error handling with invalid data."""
        mae_analyzer = MAEAnalyzer()

        # Test with empty OHLC data
        empty_data = []
        try:
            mae_default, mae_smoothed = mae_analyzer.calculate_mae(empty_data)
            # Should handle empty data gracefully
            self.assertTrue(mae_default.empty or mae_smoothed.empty)
        except Exception:
            # Exception is acceptable for empty data
            pass

        # Test price passing through with empty data
        try:
            is_passing = mae_analyzer.is_price_passing_through_mae(empty_data)
            self.assertFalse(is_passing)
        except Exception:
            # Exception is acceptable for empty data
            pass

    @patch('fyers_client.FyersClient')
    def test_mae_filter_error_recovery(self, mock_fyers_client):
        """Test MAE filter error recovery when API calls fail."""
        # Mock Fyers client that raises exception
        mock_client = Mock()
        mock_fyers_client.return_value = mock_client
        mock_client.authenticate.return_value = True
        mock_client.get_historical_data.side_effect = Exception("API Error")

        # Create equity scanner
        equity_scanner = EquityScanner(self.config)
        equity_scanner.fyers_client = mock_client

        # Create mock market data
        mock_market_data = {
            'NSE:RELIANCE-EQ': MarketData(
                symbol='NSE:RELIANCE-EQ',
                ltp=2500.0,
                volume=1000000,
                open_price=2490.0,
                high_price=2520.0,
                low_price=2480.0,
                close_price=2500.0,
                prev_close=2490.0,
                change=10.0,
                change_percent=0.4
            )
        }

        # Apply MAE filter (should handle errors gracefully)
        filtered_data = equity_scanner.apply_mae_filter(mock_market_data)

        # Should include symbol even if MAE filtering fails
        self.assertIn('NSE:RELIANCE-EQ', filtered_data)


class TestMAEIntegrationWithMarketScanners(unittest.TestCase):
    """Test MAE integration with different market scanners."""

    def setUp(self):
        """Set up test fixtures for integration tests."""
        self.test_config = {
            'general': {
                'output_dir': 'test_reports',
                'fyers_api_url': ['https://public.fyers.in/sym_details/NSE_FO.csv']
            },
            'market_types': ['EQUITY', 'INDEX', 'FUTURES', 'OPTIONS'],
            'symbols': ['NIFTY', 'BANKNIFTY'],
            'volume_filter': {
                'min_volume': 1000,
                'max_volume': ********
            },
            'ltp_filter': {
                'min_ltp_price': 1.0,
                'max_ltp_price': 1000.0
            },
            'options_filter': {
                'strike_level': 10,
                'target_months': [],
                'expiry_type': 'MONTHLY',
                'min_delta': 0.30,
                'max_delta': 0.65
            },
            'timeframe': {
                'interval': 1,
                'days_to_fetch': 15
            },
            'mae_indicator': {
                'enabled': True,
                'length': 9,
                'source': 'close',
                'offset': 0,
                'smoothing_line': 'ema',
                'smoothing_length': 9,
                'smoothing_enabled': True
            },
            'pivot_point_indicator': {
                'enabled': False,
                'calculation_type': 'WEEKLY'
            },
            'report_filter': {
                'top_n_closest': 30
            },
            'ce_pe_pairing': {
                'enabled': False,
                'min_price_percent': 0.0,
                'max_price_percent': 3.0
            }
        }

        # Create temporary config file
        self.temp_config_file = tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False)
        yaml.dump(self.test_config, self.temp_config_file, default_flow_style=False)
        self.temp_config_file.close()

        # Load configuration
        self.config = ConfigLoader(self.temp_config_file.name)

    def tearDown(self):
        """Clean up test fixtures."""
        os.unlink(self.temp_config_file.name)

    @patch('fyers_client.FyersClient')
    def test_mae_integration_with_index_scanner(self, mock_fyers_client):
        """Test MAE integration with index scanner."""
        # Mock Fyers client
        mock_client = Mock()
        mock_fyers_client.return_value = mock_client
        mock_client.authenticate.return_value = True

        # Create mock OHLC data
        ohlc_data = []
        for i in range(15):
            ohlc_data.append(OHLCData(
                timestamp=datetime.now().timestamp() + i * 3600,
                open=25000.0,
                high=25100.0,
                low=24900.0,
                close=25050.0,
                volume=1000000
            ))
        mock_client.get_historical_data.return_value = ohlc_data

        # Create index scanner
        index_scanner = IndexScanner(self.config)
        index_scanner.fyers_client = mock_client

        # Create mock market data
        mock_market_data = {
            'NSE:NIFTY50-INDEX': MarketData(
                symbol='NSE:NIFTY50-INDEX',
                ltp=25000.0,
                volume=0,  # Index typically has 0 volume
                open_price=24950.0,
                high_price=25100.0,
                low_price=24900.0,
                close_price=25000.0,
                prev_close=24950.0,
                change=50.0,
                change_percent=0.2
            )
        }

        # Apply MAE filter
        filtered_data = index_scanner.apply_mae_filter(mock_market_data)

        # Verify MAE filter was applied
        self.assertIsInstance(filtered_data, dict)

    @patch('fyers_client.FyersClient')
    def test_mae_integration_with_futures_scanner(self, mock_fyers_client):
        """Test MAE integration with futures scanner."""
        # Mock Fyers client
        mock_client = Mock()
        mock_fyers_client.return_value = mock_client
        mock_client.authenticate.return_value = True

        # Create mock OHLC data
        ohlc_data = []
        for i in range(15):
            ohlc_data.append(OHLCData(
                timestamp=datetime.now().timestamp() + i * 3600,
                open=25000.0,
                high=25100.0,
                low=24900.0,
                close=25050.0,
                volume=500000
            ))
        mock_client.get_historical_data.return_value = ohlc_data

        # Create futures scanner
        futures_scanner = FuturesScanner(self.config)
        futures_scanner.fyers_client = mock_client

        # Create mock market data
        mock_market_data = {
            'NSE:NIFTY24JULFUT': MarketData(
                symbol='NSE:NIFTY24JULFUT',
                ltp=25050.0,
                volume=500000,
                open_price=25025.0,
                high_price=25100.0,
                low_price=25000.0,
                close_price=25050.0,
                prev_close=25025.0,
                change=25.0,
                change_percent=0.1
            )
        }

        # Apply MAE filter
        filtered_data = futures_scanner.apply_mae_filter(mock_market_data)

        # Verify MAE filter was applied
        self.assertIsInstance(filtered_data, dict)


if __name__ == '__main__':
    unittest.main()
